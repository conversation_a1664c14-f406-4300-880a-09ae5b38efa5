#!/bin/bash

# Шлях до файлів
VPN_CONFIG="/home/<USER>/Downloads/ITSTEP.ovpn"
VPN_CREDENTIALS="/home/<USER>/Downloads/vpn-credentials.txt"
PID_FILE="/tmp/openvpn-itstep.pid"

# Перевірка наявності OpenVPN
check_openvpn() {
    if ! command -v openvpn &> /dev/null; then
        echo "❌ OpenVPN не встановлений!"
        echo "Для встановлення виконайте: sudo pacman -S openvpn"
        return 1
    fi
    return 0
}

# Функція для перевірки статусу VPN
check_vpn_status() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # VPN працює
        else
            rm -f "$PID_FILE"
            return 1  # VPN не працює
        fi
    else
        return 1  # VPN не працює
    fi
}

# Функція для запуску VPN
start_vpn() {
    if ! check_openvpn; then
        return 1
    fi

    if check_vpn_status; then
        echo "VPN вже запущений!"
        return 0
    fi

    echo "Запускаю VPN..."
    sudo openvpn --config "$VPN_CONFIG" --auth-user-pass "$VPN_CREDENTIALS" --daemon --writepid "$PID_FILE"
    
    # Чекаємо кілька секунд і перевіряємо статус
    sleep 3
    if check_vpn_status; then
        echo "VPN успішно запущений!"
    else
        echo "Помилка запуску VPN!"
        return 1
    fi
}

# Функція для зупинки VPN
stop_vpn() {
    if ! check_vpn_status; then
        echo "VPN не запущений!"
        return 0
    fi

    echo "Зупиняю VPN..."
    local pid=$(cat "$PID_FILE")
    sudo kill "$pid"
    sudo rm -f "$PID_FILE"
    echo "VPN зупинений!"
}

# Функція для показу статусу
show_status() {
    if check_vpn_status; then
        echo "VPN статус: ✅ Підключений"
        local pid=$(cat "$PID_FILE")
        echo "PID: $pid"
    else
        echo "VPN статус: ❌ Відключений"
    fi
}

# Основна логіка
case "$1" in
    "start"|"on"|"up")
        start_vpn
        ;;
    "stop"|"off"|"down")
        stop_vpn
        ;;
    "status"|"stat")
        show_status
        ;;
    "toggle"|"")
        if check_vpn_status; then
            stop_vpn
        else
            start_vpn
        fi
        ;;
    *)
        echo "Використання: $0 {start|stop|toggle|status}"
        echo "  start/on/up    - запустити VPN"
        echo "  stop/off/down  - зупинити VPN"
        echo "  toggle         - перемкнути стан VPN (за замовчуванням)"
        echo "  status/stat    - показати статус VPN"
        exit 1
        ;;
esac
