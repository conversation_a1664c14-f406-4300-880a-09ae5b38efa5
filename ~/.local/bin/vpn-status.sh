#!/bin/bash

# Скрипт для отримання статусу VPN для quickshell
PID_FILE="/tmp/openvpn-itstep.pid"

# Функція для перевірки статусу VPN
check_vpn_status() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # VPN працює
        else
            rm -f "$PID_FILE"
            return 1  # VPN не працює
        fi
    else
        return 1  # VPN не працює
    fi
}

# Повертаємо статус як true/false для QML
if check_vpn_status; then
    echo "true"
else
    echo "false"
fi
