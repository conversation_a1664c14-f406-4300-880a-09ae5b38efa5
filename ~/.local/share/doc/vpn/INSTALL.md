# ITSTEP VPN - Структура файлів

## 📁 Структура директорій

```
~/.config/openvpn/                    # Конфігурація OpenVPN
├── ITSTEP.ovpn                       # Конфігурація VPN сервера
└── vpn-credentials.txt               # Логін та пароль (права 600)

~/.local/bin/                         # Виконувані скрипти
├── vpn-toggle.sh                     # Основний скрипт керування VPN
└── vpn-status.sh                     # Скрипт перевірки статусу

~/.local/share/doc/vpn/               # Документація
├── VPN-README.md                     # Основна документація
└── INSTALL.md                        # Цей файл

~/.config/systemd/user/               # Systemd сервіси
└── itstep-vpn.service               # Сервіс для автозапуску (опціонально)

~/.config/fish/                       # Конфігурація Fish shell
└── config.fish                      # Містить функцію vpn та PATH

~/.config/quickshell/ii/modules/bar/  # Quickshell конфігурація
└── UtilButtons.qml                   # Містить кнопку VPN в панелі
```

## 🔒 Права доступу

- `~/.config/openvpn/` - 700 (тільки власник)
- `~/.config/openvpn/vpn-credentials.txt` - 600 (тільки власник, читання/запис)
- `~/.local/bin/vpn-*.sh` - 755 (виконувані)

## 🚀 Використання

### Командний рядок:
```bash
vpn                    # Перемкнути стан VPN
vpn start             # Запустити VPN
vpn stop              # Зупинити VPN
vpn status            # Показати статус
```

### Quickshell:
- Кнопка VPN у панелі (поруч з кнопками скріншотів та муту мікрофона)
- Автоматичне оновлення статусу кожні 2 секунди
- Зелений колір = підключено, сірий = відключено

### Systemd (опціонально):
```bash
systemctl --user enable itstep-vpn.service   # Автозапуск
systemctl --user start itstep-vpn.service    # Запустити сервіс
systemctl --user status itstep-vpn.service   # Статус сервісу
```

## 📋 Переваги нової структури

1. **Безпека**: Credentials у захищеній директорії з правильними правами
2. **Стандартність**: Використання XDG Base Directory Specification
3. **Портативність**: Всі файли в домашній директорії користувача
4. **Організованість**: Логічне розділення конфігурації, скриптів та документації
5. **Інтеграція**: Повна інтеграція з Fish shell та Quickshell
