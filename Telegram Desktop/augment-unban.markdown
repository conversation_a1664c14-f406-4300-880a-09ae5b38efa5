# Зміна hostname і пов’язаних даних в Ubuntu Linux

## 1. Зміна hostname
1. Переглянь поточний hostname:
   ```bash
   hostname
   ```
2. Встанови новий hostname (наприклад, `UBUNTU-PC-4821`):
   ```bash
   sudo hostnamectl set-hostname UBUNTU-PC-4821
   ```
3. Онови `/etc/hosts`:
   ```bash
   sudo nano /etc/hosts
   ```
   Заміни старий hostname на новий (наприклад, `127.0.0.1 UBUNTU-PC-4821`). Збережи: `Ctrl+O`, `Enter`, `Ctrl+X`.
4. Перезавантаж:
   ```bash
   sudo reboot
   ```

## 2. Зміна machine-id (аналог MachineGuid)
1. Зроби резервну копію:
   ```bash
   sudo cp /etc/machine-id /etc/machine-id.bak
   ```
2. Згенеруй новий machine-id:
   ```bash
   sudo rm /etc/machine-id
   sudo systemd-machine-id-setup
   ```
3. Онови `/var/lib/dbus/machine-id`:
   ```bash
   sudo cp /etc/machine-id /var/lib/dbus/machine-id
   ```
4. Перезавантаж:
   ```bash
   sudo reboot
   ```

⚠️ **Увага**: Зміна machine-id може вплинути на програми. Будь обережним!

## 3. Очищення даних Visual Studio Code
1. Видали папку `Code`:
   ```bash
   rm -rf ~/.config/Code
   ```
2. Видали папку `.vscode`:
   ```bash
   rm -rf ~/.vscode
   ```

## 4. Нова пошта
- Створи нову поштову скриньку на [Gmail](https://mail.google.com).

## Примітки
- Для обходу бану може знадобитися VPN для зміни IP.
- Перевір, чи сервіс не використовує інші методи ідентифікації.