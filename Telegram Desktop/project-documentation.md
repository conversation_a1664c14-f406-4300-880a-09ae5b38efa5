# Документація проекту Anime Site

## Огляд проекту

**Anime Site** - це веб-додаток для перегляду та управління аніме контентом, побудований на сучасному стеку технологій з використанням Laravel як основного фреймворку.

## Технології бекенду

### Основний стек
- **PHP 8.2+** - мова програмування
- **Laravel 11** - основний веб-фреймворк
- **PostgreSQL** - реляційна база даних
- **Filament 3.2** - адміністративна панель
- **Laravel Sanctum 4.0** - API аутентифікація та авторизація
- **Azure Blob Storage** - хмарне сховище файлів
- **Vite** - збірка фронтенд ресурсів
- **TailwindCSS** - CSS фреймворк

### Додаткові пакети
- **Laravel Actions 2.9** - організація бізнес-логіки
- **Laravel Socialite** - соціальна аутентифікація
- **Scribe 5.2** - генерація API документації
- **Laravel Notification Channels (Telegram)** - Telegram уведомлення
- **PestPHP** - тестування
- **Laravel IDE Helper** - підтримка IDE

## Архітектурні патерни проектування

### 1. Action Pattern (Команда)
**Розташування:** `app/Actions/`

Використовується для інкапсуляції бізнес-логіки в окремі класи. Кожна дія представлена окремим класом.

**Приклад:**
```php
// app/Actions/Search/PerformSearch.php
class PerformSearch
{
    use AsAction;
    
    public function handle(SearchDTO $dto): array
    {
        // Бізнес-логіка пошуку
    }
}
```

**Переваги:**
- Розділення відповідальностей
- Легке тестування
- Повторне використання логіки

### 2. Builder Pattern (Будівельник)
**Розташування:** `app/Models/Builders/`

Розширює стандартні Eloquent Query Builder для створення складних запитів.

**Приклад:**
```php
// app/Models/Builders/AnimeQueryBuilder.php
class AnimeQueryBuilder extends Builder
{
    public function popular(): self
    {
        return $this->withCount('userLists')
            ->orderByDesc('user_lists_count');
    }
    
    public function trending(int $days = 7): self
    {
        // Логіка трендових аніме
    }
}
```

### 3. Data Transfer Object (DTO) Pattern
**Розташування:** `app/DTOs/`

Використовується для передачі даних між шарами додатку.

**Приклад:**
```php
// app/DTOs/BaseDTO.php
abstract class BaseDTO
{
    public static function fromRequest(Request $request): static
    {
        // Створення DTO з запиту
    }
    
    public function toArray(): array
    {
        return get_object_vars($this);
    }
}
```

### 4. Value Object Pattern
**Розташування:** `app/ValueObjects/`

Представляє прості об'єкти, що містять значення без ідентичності.

**Приклад:**
```php
// app/ValueObjects/VideoPlayer.php
class VideoPlayer
{
    public function __construct(
        public VideoPlayerName $name,
        public string $url,
        public VideoQuality $quality,
        public string $locale_code
    ) {}
}
```

### 5. Service Pattern
**Розташування:** `app/Services/`

Інкапсулює складну бізнес-логіку та зовнішні інтеграції.

**Приклади сервісів:**
- `FileService` - робота з файлами та Azure Storage
- `LiqPayService` - інтеграція з платіжною системою
- `TmdbService` - інтеграція з The Movie Database API

### 6. Trait Pattern (Домішки)
**Розташування:** `app/Models/Traits/`

Забезпечує повторне використання функціональності між моделями.

**Основні traits:**
- `HasFiles` - робота з файлами
- `HasSeo` - SEO метадані
- `HasSearchable` - функціональність пошуку
- `HasUserInteractions` - взаємодія з користувачами

### 7. Enum Pattern
**Розташування:** `app/Enums/`

Використовує PHP 8.1 Enums для типобезпечних констант.

**Приклад:**
```php
// app/Enums/Role.php
enum Role: string
{
    case USER = 'user';
    case ADMIN = 'admin';
    case MODERATOR = 'moderator';
    
    public function name(): string
    {
        return match ($this) {
            self::USER => 'Користувач',
            self::ADMIN => 'Адміністратор',
            self::MODERATOR => 'Модератор',
        };
    }
}
```

### 8. Repository Pattern (через Eloquent)
Реалізований через Eloquent ORM з використанням кастомних Query Builders.

### 9. Factory Pattern
**Розташування:** `database/factories/`

Використовується для створення тестових даних та сідів.

### 10. Observer Pattern
Реалізований через Laravel Events та Listeners для обробки подій моделей.

## Структура бази даних

### Основні таблиці:
- `users` - користувачі системи
- `animes` - аніме контент
- `episodes` - епізоди аніме
- `people` - персони (актори, режисери)
- `studios` - аніме студії
- `tags` - теги/жанри
- `ratings` - оцінки користувачів
- `comments` - коментарі
- `user_lists` - списки користувачів
- `selections` - підбірки контенту
- `payments` - платежі
- `tariffs` - тарифні плани
- `achievements` - досягнення користувачів

### Зв'язки:
- Many-to-Many: `anime_person`, `taggables`, `selectionables`
- Polymorphic: коментарі, рейтинги, списки користувачів

## API архітектура

### Структура API:
- **Версіонування:** `/api/v1/`
- **Аутентифікація:** Laravel Sanctum (Bearer tokens)
- **Формат відповідей:** JSON API Resources
- **Документація:** Автогенерація через Scribe

### Основні ендпоінти:
- `/api/v1/animes` - управління аніме
- `/api/v1/episodes` - управління епізодами
- `/api/v1/users` - управління користувачами
- `/api/v1/search` - пошук контенту
- `/api/v1/auth` - аутентифікація

## Система файлів

### Зберігання файлів:
- **Локальне:** `storage/app/public/` (розробка)
- **Хмарне:** Azure Blob Storage (продакшн)
- **CDN:** Інтеграція з Azure CDN

### Типи файлів:
- Постери аніме
- Аватари користувачів
- Зображення епізодів
- SEO мета-зображення
- Вкладення (трейлери, скріншоти)

## Безпека

### Заходи безпеки:
- **CSRF захист** - Laravel CSRF middleware
- **XSS захист** - Blade templating escaping
- **SQL Injection** - Eloquent ORM параметризовані запити
- **Аутентифікація** - Laravel Sanctum
- **Авторизація** - Laravel Policies та Gates
- **Валідація** - Form Request classes
- **Rate Limiting** - Laravel throttling middleware

## Тестування

### Стратегія тестування:
- **Unit тести** - PestPHP
- **Feature тести** - Laravel HTTP тести
- **Database тести** - RefreshDatabase trait
- **API тести** - JSON assertions

## Розгортання

### Вимоги до сервера:
- PHP 8.2+
- PostgreSQL 13+
- Redis (кешування та черги)
- Nginx/Apache
- SSL сертифікат

### CI/CD:
- Автоматичне тестування
- Міграції бази даних
- Збірка фронтенд ресурсів
- Деплой на Azure/AWS

## Моніторинг та логування

### Логування:
- **Laravel Log** - файлове логування
- **Database queries** - моніторинг запитів
- **API requests** - логування API викликів
- **Errors** - централізоване збирання помилок

### Метрики:
- Продуктивність API
- Використання бази даних
- Активність користувачів
- Помилки системи

## Масштабування

### Горизонтальне масштабування:
- Load balancer
- Кілька серверів додатків
- Розподілена база даних
- CDN для статичних файлів

### Вертикальне масштабування:
- Оптимізація запитів
- Кешування (Redis)
- Database indexing
- Query optimization

## Підтримка та розвиток

### Документація коду:
- PHPDoc коментарі
- API документація (Scribe)
- Архітектурна документація
- Інструкції з розгортання

### Версіонування:
- Semantic versioning
- Git flow
- Feature branches
- Code review процес
