**Домашнє завдання на Java**

Створіть клас `Student` для управління інформацією про студента з такими вимогами:

1. **Інкапсуляція**:
   - Приватні поля: ім'я (String), вік (int), середній бал (double).
   - Геттери та сеттери для всіх полів.

2. **Валідація**:
   - Ім'я: не може бути порожнім або null.
   - Вік: від 16 до 100 років.
   - Середній бал: від 0.0 до 4.0.
   - Використовуйте винятки для обробки некоректних даних.

3. **Перевантаження методів**:
   - Метод `getStudentInfo` з трьома версіями:
     - Базова: повертає рядок з усією інформацією.
     - З параметром boolean: детальний або короткий формат.
     - З параметром String: повертає дані у форматі, вказаному параметром ("short" або "full").

4. **Тестування**:
   - У методі `main` створіть об'єкт класу, протестуйте всі методи та валідацію, обробляючи винятки.

**Вимога**: Код має бути чітким, з коментарями, демонструвати всі зазначені концепції.