# Технічна архітектура Anime Site

## Архітектурний огляд

Anime Site побудований за принципами **Clean Architecture** та **Domain-Driven Design (DDD)**, використовуючи Laravel як основний фреймворк з додатковими патернами для організації коду.

## Шарова архітектура

### 1. Presentation Layer (Шар представлення)
```
├── app/Http/Controllers/Api/V1/     # API контролери
├── app/Http/Resources/              # API ресурси
├── app/Http/Requests/               # Валідація запитів
├── app/Filament/                    # Адмін панель
└── resources/views/                 # Blade шаблони
```

**Відповідальності:**
- Обробка HTTP запитів
- Валідація вхідних даних
- Форматування відповідей
- Адміністративний інтерфейс

### 2. Application Layer (Шар додатку)
```
├── app/Actions/                     # Бізнес-логіка
├── app/DTOs/                        # Передача даних
├── app/Services/                    # Зовнішні інтеграції
└── app/Http/Middleware/             # Middleware
```

**Відповідальності:**
- Координація бізнес-логіки
- Обробка команд та запитів
- Інтеграція з зовнішніми сервісами
- Middleware обробка

### 3. Domain Layer (Доменний шар)
```
├── app/Models/                      # Доменні моделі
├── app/Enums/                       # Доменні енуми
├── app/ValueObjects/                # Value Objects
├── app/Policies/                    # Авторизація
└── app/Rules/                       # Бізнес правила
```

**Відповідальності:**
- Доменні моделі та логіка
- Бізнес правила
- Авторизація та політики
- Доменні об'єкти

### 4. Infrastructure Layer (Інфраструктурний шар)
```
├── app/Providers/                   # Service Providers
├── config/                          # Конфігурація
├── database/                        # Міграції, фабрики
└── storage/                         # Файлове сховище
```

**Відповідальності:**
- Конфігурація додатку
- База даних
- Файлове сховище
- Зовнішні сервіси

## Доменна модель

### Основні домени

#### 1. User Domain (Користувачі)
```
User (Aggregate Root)
├── UserSubscription
├── UserList
├── WatchHistory
├── SearchHistory
└── Achievement
```

**Основні сутності:**
- `User` - користувач системи
- `UserSubscription` - підписки користувача
- `UserList` - списки користувача (улюблені, переглянуті, тощо)
- `WatchHistory` - історія перегляду
- `Achievement` - досягнення користувача

#### 2. Content Domain (Контент)
```
Anime (Aggregate Root)
├── Episode
├── Rating
├── Comment
└── Selection

Person (Aggregate Root)
Studio (Aggregate Root)
Tag (Aggregate Root)
```

**Основні сутності:**
- `Anime` - аніме контент
- `Episode` - епізоди аніме
- `Person` - персони (актори, режисери)
- `Studio` - аніме студії
- `Tag` - теги/жанри

#### 3. Payment Domain (Платежі)
```
Payment (Aggregate Root)
├── Tariff
└── UserSubscription
```

**Основні сутності:**
- `Payment` - платежі
- `Tariff` - тарифні плани
- `UserSubscription` - підписки користувачів

#### 4. Social Domain (Соціальні функції)
```
Comment (Aggregate Root)
├── CommentLike
└── CommentReport

WatchParty (Aggregate Root)
├── WatchPartyMessage
└── WatchPartyUser
```

## Патерни інтеграції

### 1. API Gateway Pattern
Всі API запити проходять через єдину точку входу з версіонуванням:

```
/api/v1/animes          # Аніме API
/api/v1/episodes        # Епізоди API
/api/v1/users           # Користувачі API
/api/v1/auth            # Аутентифікація API
```

### 2. CQRS (Command Query Responsibility Segregation)
Розділення команд та запитів через Actions:

```php
// Команди (зміна стану)
CreateAnime::run($dto);
UpdateUser::run($user, $dto);
DeleteComment::run($comment);

// Запити (читання даних)
GetPopularAnimes::run($dto);
GetUserProfile::run($user);
PerformSearch::run($searchDto);
```

### 3. Event Sourcing (частково)
Використання Laravel Events для відстеження важливих подій:

```php
// Події моделей
UserRegistered::class
AnimeCreated::class
PaymentProcessed::class
CommentReported::class
```

## Система кешування

### Стратегія кешування
```php
// Redis для сесій та кешу
'redis' => [
    'client' => 'phpredis',
    'options' => [
        'cluster' => env('REDIS_CLUSTER', 'redis'),
        'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_database_'),
    ],
    'default' => [
        'url' => env('REDIS_URL'),
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD'),
        'port' => env('REDIS_PORT', '6379'),
        'database' => env('REDIS_DB', '0'),
    ],
]
```

### Кешовані дані
- Популярні аніме
- Результати пошуку
- Користувацькі сесії
- API відповіді
- Статичні дані (теги, студії)

## Система черг

### Queue Workers
```php
// config/queue.php
'connections' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => env('REDIS_QUEUE', 'default'),
        'retry_after' => 90,
        'block_for' => null,
    ],
]
```

### Типи завдань
- Відправка email уведомлень
- Обробка зображень
- Синхронізація з зовнішніми API
- Генерація звітів
- Очищення старих даних

## Безпека архітектури

### 1. Аутентифікація та авторизація
```php
// Laravel Sanctum для API
'sanctum' => [
    'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
        '%s%s',
        'localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1',
        Sanctum::currentApplicationUrlWithPort()
    ))),
    'expiration' => null,
    'middleware' => [
        'verify_csrf_token' => App\Http\Middleware\VerifyCsrfToken::class,
        'encrypt_cookies' => App\Http\Middleware\EncryptCookies::class,
    ],
]
```

### 2. Валідація даних
```php
// Form Request classes
class AnimeStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'kind' => ['required', Rule::enum(Kind::class)],
            'status' => ['required', Rule::enum(Status::class)],
        ];
    }
}
```

### 3. Політики доступу
```php
// app/Policies/AnimePolicy.php
class AnimePolicy
{
    public function update(User $user, Anime $anime): bool
    {
        return $user->role === Role::ADMIN || 
               $user->role === Role::MODERATOR;
    }

    public function delete(User $user, Anime $anime): bool
    {
        return $user->role === Role::ADMIN;
    }
}
```

## Моніторинг та логування

### 1. Структуроване логування
```php
// config/logging.php
'channels' => [
    'stack' => [
        'driver' => 'stack',
        'channels' => ['single', 'slack'],
        'ignore_exceptions' => false,
    ],
    'single' => [
        'driver' => 'single',
        'path' => storage_path('logs/laravel.log'),
        'level' => env('LOG_LEVEL', 'debug'),
    ],
]
```

### 2. Метрики продуктивності
- Database query time
- API response time
- Memory usage
- Cache hit ratio
- Queue processing time

### 3. Error tracking
- Exception logging
- API error rates
- Failed job tracking
- User error reports

## Масштабування архітектури

### 1. Горизонтальне масштабування
```
Load Balancer
├── App Server 1
├── App Server 2
└── App Server N

Database Cluster
├── Master (Write)
└── Slaves (Read)

Redis Cluster
├── Cache Node 1
├── Cache Node 2
└── Cache Node N
```

### 2. Мікросервісна архітектура (майбутнє)
```
API Gateway
├── User Service
├── Content Service
├── Payment Service
├── Notification Service
└── Search Service
```

### 3. CDN та статичні ресурси
```
Azure CDN
├── Images (posters, avatars)
├── Videos (trailers)
├── CSS/JS assets
└── API responses (cached)
```

## Deployment архітектура

### 1. Containerization
```dockerfile
# Dockerfile
FROM php:8.2-fpm-alpine

RUN apk add --no-cache \
    postgresql-dev \
    redis \
    nginx

COPY . /var/www/html
WORKDIR /var/www/html

RUN composer install --optimize-autoloader --no-dev
RUN npm run build
```

### 2. Orchestration
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DB_CONNECTION=pgsql
      - REDIS_HOST=redis
  
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: anime_site
  
  redis:
    image: redis:7-alpine
```

### 3. CI/CD Pipeline
```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: |
          composer install
          php artisan test
  
  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to production
        run: |
          php artisan migrate --force
          php artisan config:cache
          php artisan route:cache
```

## Висновки

Архітектура Anime Site забезпечує:

1. **Модульність** - чітке розділення на шари та домени
2. **Масштабованість** - можливість горизонтального та вертикального масштабування
3. **Безпеку** - багаторівневий захист даних та API
4. **Продуктивність** - ефективне кешування та оптимізація запитів
5. **Підтримуваність** - зрозуміла структура та документація
6. **Тестованість** - архітектура сприяє написанню тестів
7. **Гнучкість** - легко адаптується до нових вимог
