# Покращена структура бази даних для вебсайту притулків тварин

## Основні покращення:
1. ✅ Додано аудит змін (created_by, updated_by) до всіх критичних таблиць
2. ✅ Виправлено нормалізацію - видалено JSONB tags з Animals, додано AnimalTags
3. ✅ Додано MediaFiles для централізованого управління файлами (CDN стратегія)
4. ✅ Додано поля безпеки з ASP.NET Identity
5. ✅ Додано таблицю аудиту для відстеження всіх змін

---

## 1. **Species (Вид тварин)**
Сутність для зберігання видів тварин (наприклад, собака, кіт).

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **name**: VARCHAR(50) NOT NULL UNIQUE
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 2. **Breeds (Породи)**
Сутність для зберігання порід тварин, пов'язаних із видами.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **species_id**: UUID REFERENCES species(id) ON DELETE RESTRICT
- **name**: VARCHAR(100) NOT NULL
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 3. **MediaFiles (Медіафайли)**
Централізована таблиця для управління всіма медіафайлами (фото, відео) з підтримкою CDN.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **original_filename**: VARCHAR(255) NOT NULL
- **file_path**: VARCHAR(500) NOT NULL
- **cdn_url**: VARCHAR(500)
- **file_type**: VARCHAR(20) CHECK (file_type IN ('image', 'video', 'document'))
- **mime_type**: VARCHAR(100) NOT NULL
- **file_size**: BIGINT NOT NULL CHECK (file_size > 0)
- **width**: INTEGER
- **height**: INTEGER
- **duration**: INTEGER (для відео, в секундах)
- **alt_text**: VARCHAR(255)
- **is_public**: BOOLEAN DEFAULT TRUE
- **uploaded_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP

---

## 4. **Users (Користувачі)**
Розширена таблиця користувачів з полями безпеки ASP.NET Identity.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **username**: VARCHAR(256) UNIQUE
- **normalized_username**: VARCHAR(256) UNIQUE
- **email**: VARCHAR(256) NOT NULL UNIQUE
- **normalized_email**: VARCHAR(256) NOT NULL UNIQUE
- **email_confirmed**: BOOLEAN DEFAULT FALSE
- **password_hash**: VARCHAR(255) NOT NULL
- **security_stamp**: VARCHAR(255)
- **concurrency_stamp**: VARCHAR(255) DEFAULT gen_random_uuid()
- **phone_number**: VARCHAR(20)
- **phone_number_confirmed**: BOOLEAN DEFAULT FALSE
- **two_factor_enabled**: BOOLEAN DEFAULT FALSE
- **lockout_end**: TIMESTAMP WITH TIME ZONE
- **lockout_enabled**: BOOLEAN DEFAULT TRUE
- **access_failed_count**: INTEGER DEFAULT 0
- **first_name**: VARCHAR(50)
- **last_name**: VARCHAR(50)
- **address**: TEXT
- **role**: VARCHAR(20) CHECK (role IN ('Volunteer', 'Adopter', 'Admin', 'Moderator'))
- **preferences**: JSONB
- **lifestyle_data**: JSONB
- **points**: INTEGER DEFAULT 0 CHECK (points >= 0)
- **last_login**: TIMESTAMP WITH TIME ZONE
- **profile_photo_id**: UUID REFERENCES media_files(id) ON DELETE SET NULL
- **language**: VARCHAR(10) DEFAULT 'uk'
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 5. **UserClaims (Права користувачів)**
Таблиця для зберігання додаткових прав користувачів (ASP.NET Identity).

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **user_id**: UUID REFERENCES users(id) ON DELETE CASCADE
- **claim_type**: VARCHAR(255) NOT NULL
- **claim_value**: VARCHAR(255)
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP

---

## 6. **UserTokens (Токени користувачів)**
Таблиця для зберігання токенів (reset password, email confirmation, etc.).

- **user_id**: UUID REFERENCES users(id) ON DELETE CASCADE
- **login_provider**: VARCHAR(255) NOT NULL
- **name**: VARCHAR(255) NOT NULL
- **value**: VARCHAR(500)
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **expires_at**: TIMESTAMP WITH TIME ZONE
- PRIMARY KEY (user_id, login_provider, name)

---

## 7. **AuditLog (Журнал аудиту)**
Централізована таблиця для відстеження всіх змін у критичних таблицях.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **table_name**: VARCHAR(50) NOT NULL
- **record_id**: UUID NOT NULL
- **action**: VARCHAR(10) CHECK (action IN ('INSERT', 'UPDATE', 'DELETE'))
- **old_values**: JSONB
- **new_values**: JSONB
- **changed_fields**: TEXT[]
- **user_id**: UUID REFERENCES users(id) ON DELETE SET NULL
- **ip_address**: INET
- **user_agent**: TEXT
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP

---

## 8. **Shelters (Притулки)**
Сутність для зберігання інформації про притулки компанії.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **name**: VARCHAR(100) NOT NULL
- **address**: TEXT NOT NULL
- **coordinates**: GEOMETRY(POINT, 4326) NOT NULL
- **contact_phone**: VARCHAR(20)
- **contact_email**: VARCHAR(255)
- **description**: TEXT
- **capacity**: INTEGER NOT NULL CHECK (capacity >= 0)
- **current_occupancy**: INTEGER NOT NULL DEFAULT 0 CHECK (current_occupancy >= 0)
- **virtual_tour_url**: VARCHAR(255)
- **needs**: TEXT
- **working_hours**: VARCHAR(100)
- **website_url**: VARCHAR(255)
- **social_media**: JSONB
- **manager_id**: UUID REFERENCES users(id) ON DELETE SET NULL
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 9. **ShelterMediaFiles (Медіафайли притулків)**
Зв'язкова таблиця між притулками та медіафайлами.

- **shelter_id**: UUID REFERENCES shelters(id) ON DELETE CASCADE
- **media_file_id**: UUID REFERENCES media_files(id) ON DELETE CASCADE
- **display_order**: INTEGER DEFAULT 0
- **is_primary**: BOOLEAN DEFAULT FALSE
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- PRIMARY KEY (shelter_id, media_file_id)

---

## 10. **Animals (Тварини)**
Покращена сутність для зберігання інформації про тварин (без JSONB tags).

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **name**: VARCHAR(50) NOT NULL
- **species_id**: UUID REFERENCES species(id) ON DELETE RESTRICT
- **breed_id**: UUID REFERENCES breeds(id) ON DELETE SET NULL
- **age**: INTEGER CHECK (age >= 0)
- **gender**: VARCHAR(10) CHECK (gender IN ('Male', 'Female'))
- **description**: TEXT
- **health_status**: TEXT
- **shelter_id**: UUID REFERENCES shelters(id) ON DELETE CASCADE
- **status**: VARCHAR(20) CHECK (status IN ('Available', 'Adopted', 'Reserved', 'InTreatment'))
- **adoption_requirements**: TEXT
- **story**: TEXT
- **compatibility_score**: FLOAT CHECK (compatibility_score >= 0 AND compatibility_score <= 1)
- **microchip_id**: VARCHAR(50) UNIQUE
- **weight**: FLOAT CHECK (weight > 0)
- **last_medical_check**: TIMESTAMP WITH TIME ZONE
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 11. **AnimalMediaFiles (Медіафайли тварин)**
Зв'язкова таблиця між тваринами та медіафайлами.

- **animal_id**: UUID REFERENCES animals(id) ON DELETE CASCADE
- **media_file_id**: UUID REFERENCES media_files(id) ON DELETE CASCADE
- **display_order**: INTEGER DEFAULT 0
- **is_primary**: BOOLEAN DEFAULT FALSE
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- PRIMARY KEY (animal_id, media_file_id)

---

## 12. **Tags (Теги)**
Сутність для тегів з підтримкою вкладеності.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **name**: VARCHAR(50) NOT NULL UNIQUE
- **is_category**: BOOLEAN DEFAULT FALSE
- **parent_tag_id**: UUID REFERENCES tags(id) ON DELETE SET NULL
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 13. **AnimalTags (Зв'язок тварин і тегів)**
Нормалізована зв'язкова таблиця між тваринами та тегами.

- **animal_id**: UUID REFERENCES animals(id) ON DELETE CASCADE
- **tag_id**: UUID REFERENCES tags(id) ON DELETE CASCADE
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- PRIMARY KEY (animal_id, tag_id)

---

## 14. **PaymentMethods (Методи оплати)**
Сутність для зберігання доступних методів оплати.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **name**: VARCHAR(50) NOT NULL UNIQUE
- **is_active**: BOOLEAN DEFAULT TRUE
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 15. **Donations (Пожертви)**
Сутність для управління фінансовими внесками з аудитом.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **user_id**: UUID REFERENCES users(id) ON DELETE SET NULL
- **amount**: DECIMAL(10,2) NOT NULL CHECK (amount > 0)
- **shelter_id**: UUID REFERENCES shelters(id) ON DELETE SET NULL
- **payment_method_id**: UUID REFERENCES payment_methods(id) ON DELETE RESTRICT
- **status**: VARCHAR(20) CHECK (status IN ('Pending', 'Completed', 'Failed', 'Refunded'))
- **transaction_id**: VARCHAR(255)
- **purpose**: VARCHAR(255)
- **recurring**: BOOLEAN DEFAULT FALSE
- **anonymous**: BOOLEAN DEFAULT FALSE
- **donation_date**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 16. **VolunteerTasks (Волонтерські завдання)**
Сутність для управління волонтерськими активностями з аудитом.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **shelter_id**: UUID REFERENCES shelters(id) ON DELETE CASCADE
- **title**: VARCHAR(100) NOT NULL
- **description**: TEXT
- **date**: DATE NOT NULL
- **duration**: INTEGER CHECK (duration > 0)
- **required_volunteers**: INTEGER CHECK (required_volunteers > 0)
- **assigned_volunteers**: JSONB
- **status**: VARCHAR(20) CHECK (status IN ('Open', 'InProgress', 'Completed', 'Cancelled'))
- **points_reward**: INTEGER CHECK (points_reward >= 0)
- **location**: GEOMETRY(POINT, 4326)
- **skills_required**: JSONB
- **priority**: VARCHAR(20) CHECK (priority IN ('Low', 'Medium', 'High'))
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 17. **AdoptionApplications (Заявки на адопцію)**
Сутність для обробки заявок на адопцію тварин з повним аудитом.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **user_id**: UUID REFERENCES users(id) ON DELETE CASCADE
- **animal_id**: UUID REFERENCES animals(id) ON DELETE CASCADE
- **shelter_id**: UUID REFERENCES shelters(id) ON DELETE CASCADE
- **status**: VARCHAR(20) CHECK (status IN ('Pending', 'Approved', 'Rejected', 'InProgress'))
- **application_date**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **comment**: TEXT
- **admin_notes**: TEXT
- **approved_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **rejection_reason**: TEXT
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 18. **AdoptionApplicationMediaFiles (Медіафайли заявок)**
Зв'язкова таблиця для фотографій житла заявників.

- **application_id**: UUID REFERENCES adoption_applications(id) ON DELETE CASCADE
- **media_file_id**: UUID REFERENCES media_files(id) ON DELETE CASCADE
- **file_type**: VARCHAR(20) CHECK (file_type IN ('home_photo', 'document'))
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- PRIMARY KEY (application_id, media_file_id)

---

## Пояснення щодо CDN стратегії:

**CDN (Content Delivery Network)** - це мережа серверів, розташованих у різних географічних точках, яка дозволяє:

1. **Швидкість**: Файли завантажуються з найближчого до користувача сервера
2. **Масштабування**: Зменшує навантаження на основний сервер
3. **Надійність**: Резервування файлів на кількох серверах
4. **Економія**: Зменшує витрати на трафік основного сервера

**Як це працює в нашій схемі:**
- Поле `file_path` - шлях до файлу на локальному сервері
- Поле `cdn_url` - URL файлу в CDN (наприклад, CloudFlare, AWS CloudFront)
- При завантаженні файлу він зберігається локально і синхронізується з CDN
- Користувачі отримують файли через CDN URL для кращої продуктивності

**Приклади CDN провайдерів:**
- AWS CloudFront
- Cloudflare
- Azure CDN
- Google Cloud CDN

---

## 19. **LostPets (Загублені тварини)**
Сутність для заявок про загублених тварин з аудитом.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **user_id**: UUID REFERENCES users(id) ON DELETE CASCADE
- **species_id**: UUID REFERENCES species(id) ON DELETE RESTRICT
- **breed_id**: UUID REFERENCES breeds(id) ON DELETE SET NULL
- **name**: VARCHAR(50)
- **description**: TEXT
- **last_seen_location**: GEOMETRY(POINT, 4326)
- **last_seen_date**: TIMESTAMP WITH TIME ZONE
- **status**: VARCHAR(20) CHECK (status IN ('Lost', 'Found', 'Reunited'))
- **admin_notes**: TEXT
- **reward**: DECIMAL(10,2) CHECK (reward >= 0)
- **contact_alternative**: VARCHAR(255)
- **microchip_id**: VARCHAR(50)
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 20. **LostPetMediaFiles (Медіафайли загублених тварин)**
Зв'язкова таблиця для фотографій загублених тварин.

- **lost_pet_id**: UUID REFERENCES lost_pets(id) ON DELETE CASCADE
- **media_file_id**: UUID REFERENCES media_files(id) ON DELETE CASCADE
- **display_order**: INTEGER DEFAULT 0
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- PRIMARY KEY (lost_pet_id, media_file_id)

---

## 21. **AnimalAidRequests (Допомога тваринам)**
Сутність для запитів на допомогу тваринам з аудитом.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **user_id**: UUID REFERENCES users(id) ON DELETE SET NULL
- **shelter_id**: UUID REFERENCES shelters(id) ON DELETE SET NULL
- **title**: VARCHAR(100) NOT NULL
- **description**: TEXT
- **category**: VARCHAR(50) CHECK (category IN ('Food', 'Medical', 'Equipment', 'Other'))
- **urgency**: VARCHAR(20) CHECK (urgency IN ('Low', 'Medium', 'High'))
- **status**: VARCHAR(20) CHECK (status IN ('Open', 'InProgress', 'Fulfilled', 'Cancelled'))
- **estimated_cost**: DECIMAL(10,2) CHECK (estimated_cost >= 0)
- **fulfilled_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 22. **AidRequestMediaFiles (Медіафайли запитів допомоги)**
Зв'язкова таблиця для фотографій запитів допомоги.

- **aid_request_id**: UUID REFERENCES animal_aid_requests(id) ON DELETE CASCADE
- **media_file_id**: UUID REFERENCES media_files(id) ON DELETE CASCADE
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- PRIMARY KEY (aid_request_id, media_file_id)

---

## 23. **NotificationTypes (Типи нотифікацій)**
Сутність для зберігання типів сповіщень.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **name**: VARCHAR(50) NOT NULL UNIQUE
- **description**: TEXT
- **is_active**: BOOLEAN DEFAULT TRUE
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 24. **Notifications (Нотифікації)**
Сутність для управління сповіщеннями користувачів з аудитом.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **user_id**: UUID REFERENCES users(id) ON DELETE CASCADE
- **notification_type_id**: UUID REFERENCES notification_types(id) ON DELETE RESTRICT
- **title**: VARCHAR(100) NOT NULL
- **message**: TEXT NOT NULL
- **is_read**: BOOLEAN DEFAULT FALSE
- **priority**: VARCHAR(20) CHECK (priority IN ('Low', 'Medium', 'High'))
- **related_entity**: JSONB
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **sent_at**: TIMESTAMP WITH TIME ZONE
- **read_at**: TIMESTAMP WITH TIME ZONE

---

## 25. **ArticleCategories (Категорії статей)**
Сутність для категорій освітнього контенту з аудитом.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **name**: VARCHAR(50) NOT NULL UNIQUE
- **description**: TEXT
- **is_active**: BOOLEAN DEFAULT TRUE
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 26. **Articles (Статті)**
Сутність для освітнього контенту з аудитом.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **title**: VARCHAR(255) NOT NULL
- **content**: TEXT NOT NULL
- **category_id**: UUID REFERENCES article_categories(id) ON DELETE SET NULL
- **author_id**: UUID REFERENCES users(id) ON DELETE SET NULL
- **status**: VARCHAR(20) CHECK (status IN ('Draft', 'Published', 'Archived'))
- **thumbnail_id**: UUID REFERENCES media_files(id) ON DELETE SET NULL
- **published_at**: TIMESTAMP WITH TIME ZONE
- **views**: INTEGER DEFAULT 0 CHECK (views >= 0)
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 27. **Events (Події)**
Сутність для організації подій з аудитом.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **shelter_id**: UUID REFERENCES shelters(id) ON DELETE SET NULL
- **title**: VARCHAR(100) NOT NULL
- **description**: TEXT
- **event_date**: TIMESTAMP WITH TIME ZONE
- **location**: GEOMETRY(POINT, 4326)
- **address**: TEXT
- **type**: VARCHAR(50) CHECK (type IN ('AdoptionDay', 'Fundraiser', 'Webinar', 'VolunteerTraining'))
- **participants**: JSONB
- **status**: VARCHAR(20) CHECK (status IN ('Planned', 'Ongoing', 'Completed', 'Cancelled'))
- **max_participants**: INTEGER
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 28. **Inventory (Інвентар)**
Сутність для управління запасами притулків з аудитом.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **shelter_id**: UUID REFERENCES shelters(id) ON DELETE CASCADE
- **item_name**: VARCHAR(100) NOT NULL
- **category**: VARCHAR(50) CHECK (category IN ('Food', 'Medical', 'Equipment', 'Other'))
- **quantity**: INTEGER NOT NULL CHECK (quantity >= 0)
- **unit**: VARCHAR(20)
- **min_threshold**: INTEGER CHECK (min_threshold >= 0)
- **cost_per_unit**: DECIMAL(10,2) CHECK (cost_per_unit >= 0)
- **supplier**: VARCHAR(100)
- **expiration_date**: DATE
- **last_updated**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 29. **SuccessStories (Історії успіху)**
Сутність для історій успішних адопцій з аудитом.

- **id**: UUID PRIMARY KEY DEFAULT gen_random_uuid()
- **animal_id**: UUID REFERENCES animals(id) ON DELETE CASCADE
- **user_id**: UUID REFERENCES users(id) ON DELETE SET NULL
- **title**: VARCHAR(100) NOT NULL
- **content**: TEXT NOT NULL
- **published_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **views**: INTEGER DEFAULT 0 CHECK (views >= 0)
- **is_featured**: BOOLEAN DEFAULT FALSE
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **updated_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- **created_by**: UUID REFERENCES users(id) ON DELETE SET NULL
- **updated_by**: UUID REFERENCES users(id) ON DELETE SET NULL

---

## 30. **SuccessStoryMediaFiles (Медіафайли історій успіху)**
Зв'язкова таблиця для медіафайлів історій успіху.

- **success_story_id**: UUID REFERENCES success_stories(id) ON DELETE CASCADE
- **media_file_id**: UUID REFERENCES media_files(id) ON DELETE CASCADE
- **display_order**: INTEGER DEFAULT 0
- **created_at**: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- PRIMARY KEY (success_story_id, media_file_id)

---

## Рекомендовані індекси для оптимізації продуктивності:

```sql
-- Геопросторові індекси
CREATE INDEX idx_shelters_coordinates ON shelters USING GIST (coordinates);
CREATE INDEX idx_lost_pets_location ON lost_pets USING GIST (last_seen_location);
CREATE INDEX idx_volunteer_tasks_location ON volunteer_tasks USING GIST (location);
CREATE INDEX idx_events_location ON events USING GIST (location);

-- JSONB індекси
CREATE INDEX idx_users_preferences ON users USING GIN (preferences);
CREATE INDEX idx_users_lifestyle_data ON users USING GIN (lifestyle_data);
CREATE INDEX idx_volunteer_tasks_skills ON volunteer_tasks USING GIN (skills_required);
CREATE INDEX idx_notifications_related_entity ON notifications USING GIN (related_entity);

-- Композитні індекси для частих запитів
CREATE INDEX idx_animals_status_shelter ON animals (status, shelter_id);
CREATE INDEX idx_animals_species_breed ON animals (species_id, breed_id);
CREATE INDEX idx_adoption_applications_status_date ON adoption_applications (status, application_date);
CREATE INDEX idx_donations_user_date ON donations (user_id, donation_date);
CREATE INDEX idx_volunteer_tasks_status_date ON volunteer_tasks (status, date);
CREATE INDEX idx_notifications_user_read ON notifications (user_id, is_read);

-- Індекси для аудиту
CREATE INDEX idx_audit_log_table_record ON audit_log (table_name, record_id);
CREATE INDEX idx_audit_log_user_date ON audit_log (user_id, created_at);
CREATE INDEX idx_audit_log_created_at ON audit_log (created_at);

-- Індекси для медіафайлів
CREATE INDEX idx_media_files_type ON media_files (file_type);
CREATE INDEX idx_media_files_uploaded_by ON media_files (uploaded_by);
CREATE INDEX idx_media_files_created_at ON media_files (created_at);

-- Унікальні індекси для безпеки
CREATE UNIQUE INDEX idx_users_normalized_email ON users (normalized_email);
CREATE UNIQUE INDEX idx_users_normalized_username ON users (normalized_username) WHERE normalized_username IS NOT NULL;
```

---

## Тригери для автоматичного аудиту:

```sql
-- Функція для аудиту змін
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (table_name, record_id, action, old_values, user_id, created_at)
        VALUES (TG_TABLE_NAME, OLD.id, 'DELETE', row_to_json(OLD),
                COALESCE(current_setting('app.current_user_id', true)::UUID, NULL),
                CURRENT_TIMESTAMP);
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log (table_name, record_id, action, old_values, new_values, user_id, created_at)
        VALUES (TG_TABLE_NAME, NEW.id, 'UPDATE', row_to_json(OLD), row_to_json(NEW),
                COALESCE(current_setting('app.current_user_id', true)::UUID, NULL),
                CURRENT_TIMESTAMP);
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (table_name, record_id, action, new_values, user_id, created_at)
        VALUES (TG_TABLE_NAME, NEW.id, 'INSERT', row_to_json(NEW),
                COALESCE(current_setting('app.current_user_id', true)::UUID, NULL),
                CURRENT_TIMESTAMP);
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Створення тригерів для критичних таблиць
CREATE TRIGGER audit_animals AFTER INSERT OR UPDATE OR DELETE ON animals
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_users AFTER INSERT OR UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_adoption_applications AFTER INSERT OR UPDATE OR DELETE ON adoption_applications
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_donations AFTER INSERT OR UPDATE OR DELETE ON donations
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_shelters AFTER INSERT OR UPDATE OR DELETE ON shelters
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```

---

## Додаткові рекомендації:

### 1. **Безпека**
- Використовувати bcrypt або Argon2 для хешування паролів
- Реалізувати rate limiting для API
- Додати HTTPS та CSP заголовки
- Регулярно оновлювати security_stamp при зміні критичних даних

### 2. **Продуктивність**
- Використовувати connection pooling
- Реалізувати кешування з Redis
- Партиціонувати великі таблиці за датами
- Архівувати старі записи аудиту

### 3. **Моніторинг**
- Налаштувати алерти для критичних операцій
- Моніторити розмір таблиць та індексів
- Відстежувати повільні запити
- Регулярно аналізувати план виконання запитів

### 4. **Резервне копіювання**
- Щоденні повні бекапи
- Інкрементальні бекапи кожні 4 години
- Тестування відновлення даних
- Географічно розподілені копії

### 5. **CDN та медіафайли**
- Автоматична синхронізація з CDN
- Оптимізація зображень (WebP, різні розміри)
- Lazy loading для медіаконтенту
- Видалення неактивних файлів
