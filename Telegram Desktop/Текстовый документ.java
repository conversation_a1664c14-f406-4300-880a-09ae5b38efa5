public class Student {
    private String name;
    private int age;
    private double gpa;

    public Student(String name, int age, double gpa) {
        setName(name);
        setAge(age);
        setGpa(gpa);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Ім'я не може бути порожнім або null.");
        }
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        if (age < 16 || age > 100) {
            throw new IllegalArgumentException("Вік має бути від 16 до 100 років.");
        }
        this.age = age;
    }

    public double getGpa() {
        return gpa;
    }

    public void setGpa(double gpa) {
        if (gpa < 0.0 || gpa > 4.0) {
            throw new IllegalArgumentException("Середній бал має бути від 0.0 до 4.0.");
        }
        this.gpa = gpa;
    }

    public String getStudentInfo() {
        return "Ім'я: " + name + ", Вік: " + age + ", Середній бал: " + gpa;
    }

    public String getStudentInfo(boolean detailed) {
        if (detailed) {
            return getStudentInfo();
        } else {
            return name + ", " + gpa;
        }
    }

    public String getStudentInfo(String format) {
        if (format.equalsIgnoreCase("full")) {
            return getStudentInfo();
        } else if (format.equalsIgnoreCase("short")) {
            return name + ", " + gpa;
        } else {
            return "Невідомий формат. Використовуйте 'short' або 'full'.";
        }
    }

    public static void main(String[] args) {
        try {
            Student student = new Student("Олександр", 20, 3.5);

            System.out.println(student.getStudentInfo());
            student.setName("Марія");
            student.setAge(22);
            student.setGpa(3.8);
            System.out.println(student.getStudentInfo());

            System.out.println(student.getStudentInfo(true));
            System.out.println(student.getStudentInfo(false));
            System.out.println(student.getStudentInfo("short"));
            System.out.println(student.getStudentInfo("full"));
            System.out.println(student.getStudentInfo("unknown"));


            student.setName("");
        } catch (IllegalArgumentException e) {
            System.out.println("Помилка: " + e.getMessage());
        }

        try {
            Student badStudent = new Student("Іван", 15, 5.0);
        } catch (IllegalArgumentException e) {
            System.out.println("Помилка при створенні студента: " + e.getMessage());
        }
    }
}
