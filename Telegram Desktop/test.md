**Тема**: Створення інтерактивного організатора подій

**Підзавдання**:
1. **Створення форми**:
   - Створіть HTML-форму з полями: назва події (текст), дата події (дата), тип події (випадаючий список), опис події (textarea) та додаткові опції (checkbox).
   - Додайте стилізацію за допомогою CSS для приємного вигляду (вже включено в код).

2. **Валідація форми**:
   - Перевірте, що назва події містить щонайменше 3 символи.
   - Переконайтеся, що дата події не в минулому.
   - Тип події має бути обов’язково обраним.
   - Опис події не повинен перевищувати 500 символів.
   - Виведіть повідомлення про помилки під відповідними полями, якщо валідація не пройдена.

3. **Зберігання даних**:
   - Зберігайте дані про події в localStorage у форматі JSON.
   - Кожна подія має містити назву, дату, тип, опис та обрані опції.

4. **Відображення подій**:
   - Після додавання події відображайте її в списку нижче форми.
   - Показуйте всі збережені події при завантаженні сторінки.
   - Для кожної події відображайте назву, тип, дату, опис та обрані опції.

5. **Видалення подій**:
   - Додайте кнопку "Видалити" для кожної події в списку.
   - При натисканні видаляйте подію з localStorage та оновлюйте список.

6. **Додаткове завдання (опціонально)**:
   - Додайте можливість редагувати існуючі події (наприклад, кнопка "Редагувати", яка заповнює форму даними вибраної події).
   - Додайте фільтр за типом події (наприклад, показувати лише конференції).
   - Експортуйте список подій у JSON-файл для завантаження.
