# Документація проекту Anime Site

Ця папка містить повну технічну документацію проекту Anime Site.

## Структура документації

### 📋 [Загальна документація проекту](project-documentation.md)
Огляд проекту, технологій бекенду, архітектури та основних компонентів.

### 🏗️ [Патерни проектування](design-patterns.md)
Детальний опис всіх використаних патернів проектування з прикладами коду.

### 🏛️ [Технічна архітектура](technical-architecture.md)
Глибокий аналіз архітектури системи, шарів додатку та інтеграційних рішень.

### 📊 [Підсумок патернів](patterns-summary.md)
Повний перелік та статистика використання патернів проектування в проекті.

### 📁 [Робота з файлами](file-handling.md)
Документація по роботі з файлами в моделях (існуюча документація).

## Швидкий старт

### Основні технології
- **Backend:** Laravel 11, PHP 8.2+
- **Database:** PostgreSQL
- **Admin Panel:** Filament 3.2
- **API Auth:** Laravel Sanctum
- **File Storage:** Azure Blob Storage
- **Frontend Build:** Vite + TailwindCSS

### Ключові патерни
1. **Action Pattern** - бізнес-логіка в `app/Actions/`
2. **Builder Pattern** - складні запити в `app/Models/Builders/`
3. **DTO Pattern** - передача даних в `app/DTOs/`
4. **Service Pattern** - зовнішні інтеграції в `app/Services/`
5. **Value Objects** - доменні об'єкти в `app/ValueObjects/`

### Структура проекту
```
app/
├── Actions/           # Бізнес-логіка (Action Pattern)
├── DTOs/             # Data Transfer Objects
├── Enums/            # PHP 8.1 Enums
├── Http/             # Controllers, Requests, Resources
├── Models/           # Eloquent Models
│   ├── Builders/     # Query Builders
│   └── Traits/       # Model Traits
├── Policies/         # Authorization Policies
├── Providers/        # Service Providers
├── Services/         # Business Services
└── ValueObjects/     # Domain Value Objects
```

## Для розробників

### Додавання нової функціональності
1. Створіть Action в `app/Actions/`
2. Додайте DTO в `app/DTOs/` (якщо потрібно)
3. Створіть Request validation в `app/Http/Requests/`
4. Додайте API Resource в `app/Http/Resources/`
5. Оновіть контролер в `app/Http/Controllers/Api/V1/`

### Робота з базою даних
1. Використовуйте Query Builders для складних запитів
2. Додавайте Scopes для повторюваної логіки
3. Використовуйте Traits для спільної функціональності
4. Створюйте Factories для тестових даних

### Тестування
1. Пишіть Unit тести для Actions
2. Створюйте Feature тести для API endpoints
3. Використовуйте Factories для тестових даних
4. Мокайте зовнішні сервіси

## Корисні посилання

- [Laravel Documentation](https://laravel.com/docs)
- [Filament Documentation](https://filamentphp.com/docs)
- [Laravel Actions](https://laravelactions.com/)
- [PestPHP Testing](https://pestphp.com/)

## Контакти

Для питань по архітектурі та патернах проектування звертайтесь до технічного лідера проекту.
