# Патерни проектування в Anime Site

## Огляд використаних патернів

Цей документ детально описує всі патерни проектування, що використовуються в проекті Anime Site, з прикладами коду та поясненнями їх застосування.

## 1. Action Pattern (Команда)

### Опис
Action Pattern інкапсулює бізнес-логіку в окремі класи, що можуть бути викликані з різних місць додатку (контролери, команди, черги, тощо).

### Реалізація
Використовується пакет `lorisleiva/laravel-actions` для стандартизації підходу.

### Структура
```
app/Actions/
├── Animes/
│   ├── CreateAnime.php
│   ├── GetPopularAnimes.php
│   └── SearchAnimes.php
├── Auth/
│   ├── LoginUser.php
│   ├── RegisterUser.php
│   └── ResetPassword.php
├── Search/
│   ├── PerformSearch.php
│   └── GetSearchSuggestions.php
└── Users/
    ├── UpdateUserProfile.php
    └── UploadUserAvatar.php
```

### Приклад використання
```php
// app/Actions/Search/PerformSearch.php
class PerformSearch
{
    use AsAction;

    public function handle(SearchDTO $dto): array
    {
        if (empty($dto->query)) {
            return [];
        }

        $results = [];
        
        // Пошук аніме
        $animes = Anime::where('title', 'ILIKE', "%{$dto->query}%")
            ->limit(10)
            ->get();
        
        if ($animes->isNotEmpty()) {
            $results['animes'] = $animes;
        }

        return $results;
    }
}

// Використання в контролері
public function search(SearchRequest $request)
{
    $dto = SearchDTO::fromRequest($request);
    $results = PerformSearch::run($dto);
    
    return response()->json($results);
}
```

### Переваги
- **Розділення відповідальностей** - кожна дія має одну відповідальність
- **Повторне використання** - actions можна викликати з різних місць
- **Тестування** - легко тестувати ізольовано
- **Читабельність** - зрозуміла структура коду

## 2. Builder Pattern (Будівельник)

### Опис
Розширює стандартні Eloquent Query Builders для створення складних, повторно використовуваних запитів.

### Структура
```
app/Models/Builders/
├── AnimeQueryBuilder.php
├── UserQueryBuilder.php
├── EpisodeQueryBuilder.php
└── PersonQueryBuilder.php
```

### Приклад реалізації
```php
// app/Models/Builders/AnimeQueryBuilder.php
class AnimeQueryBuilder extends Builder
{
    public function popular(): self
    {
        return $this->withCount('userLists')
            ->orderByDesc('user_lists_count');
    }

    public function trending(int $days = 7): self
    {
        $date = Carbon::now()->subDays($days);

        return $this->withCount(['ratings' => function ($query) use ($date) {
                $query->where('created_at', '>=', $date);
            }])
            ->withCount(['comments' => function ($query) use ($date) {
                $query->where('created_at', '>=', $date);
            }])
            ->orderByRaw('(ratings_count * 2 + comments_count) DESC');
    }

    public function ofKind(Kind $kind): self
    {
        return $this->where('kind', $kind->value);
    }

    public function withTags(array $tagIds): self
    {
        return $this->whereHas('tags', function ($query) use ($tagIds) {
            $query->whereIn('tags.id', $tagIds);
        });
    }
}

// Використання в моделі
class Anime extends Model
{
    public function newEloquentBuilder($query): AnimeQueryBuilder
    {
        return new AnimeQueryBuilder($query);
    }
}

// Використання
$popularAnimes = Anime::popular()->limit(10)->get();
$trendingAnimes = Anime::trending(14)->get();
$actionAnimes = Anime::withTags([1, 2, 3])->get();
```

## 3. Data Transfer Object (DTO) Pattern

### Опис
DTO використовуються для передачі даних між шарами додатку, забезпечуючи типобезпеку та валідацію.

### Базовий клас
```php
// app/DTOs/BaseDTO.php
abstract class BaseDTO
{
    protected static array $fields = [];

    public static function fromRequest(Request $request): static
    {
        $fields = static::$fields;
        $args = [];

        foreach ($fields as $key => $value) {
            $requestKey = is_string($key) ? $key : $value;
            $propertyName = is_string($key) ? $value : $key;
            
            $args[$propertyName] = $request->input($requestKey);
        }

        return new static(...$args);
    }

    public function toArray(): array
    {
        return get_object_vars($this);
    }
}
```

### Приклад DTO
```php
// app/DTOs/Search/SearchDTO.php
class SearchDTO extends BaseDTO
{
    protected static array $fields = [
        'query',
        'type',
        'limit',
        'offset'
    ];

    public function __construct(
        public string $query,
        public ?string $type = null,
        public int $limit = 20,
        public int $offset = 0
    ) {}
}
```

## 4. Value Object Pattern

### Опис
Value Objects представляють прості об'єкти, що містять значення без ідентичності. Вони незмінні та порівнюються за значенням.

### Приклади
```php
// app/ValueObjects/VideoPlayer.php
class VideoPlayer
{
    public function __construct(
        public VideoPlayerName $name,
        public string $url,
        public string $file_url,
        public string $dubbing,
        public VideoQuality $quality,
        public string $locale_code
    ) {}
}

// app/ValueObjects/ApiSource.php
class ApiSource
{
    public function __construct(
        public ApiSourceName $name,
        public string $url,
        public array $headers = []
    ) {}
}
```

## 5. Service Pattern

### Опис
Services інкапсулюють складну бізнес-логіку та зовнішні інтеграції.

### Приклад FileService
```php
// app/Services/FileService.php
class FileService
{
    public function storeFile(
        ?UploadedFile $file, 
        string $directory, 
        ?string $oldFilePath = null, 
        bool $public = true
    ): ?string {
        if (!$file) {
            return null;
        }

        // Видалення старого файлу
        if ($oldFilePath) {
            $this->deleteFile($oldFilePath, $public);
        }

        // Генерація унікального імені
        $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();

        // Збереження файлу
        $disk = $public ? 'public' : 'local';
        $path = $file->storeAs($directory, $filename, $disk);

        return $path;
    }

    public function storeBase64ImageToAzure(
        string $base64, 
        string $directory, 
        ?string $oldFilePath = null
    ): string {
        // Валідація base64
        if (!preg_match('/^data:image\/(\w+);base64,/', $base64, $type)) {
            throw new \InvalidArgumentException('Невірний base64 формат');
        }

        $extension = $type[1];
        $base64Data = substr($base64, strpos($base64, ',') + 1);
        $decoded = base64_decode($base64Data);

        if ($decoded === false) {
            throw new \RuntimeException('Не вдалося декодувати base64 зображення');
        }

        $filename = $directory . '/' . Str::ulid() . '.' . $extension;

        // Збереження в Azure
        Storage::disk('azure')->put($filename, $decoded, 'public');

        // Видалення старого файлу
        if ($oldFilePath) {
            Storage::disk('azure')->delete($this->extractPathFromUrl($oldFilePath));
        }

        return $filename;
    }
}
```

## 6. Trait Pattern (Домішки)

### Опис
Traits забезпечують повторне використання коду між класами без наслідування.

### Приклад HasFiles Trait
```php
// app/Models/Traits/HasFiles.php
trait HasFiles
{
    protected FileService $fileService;

    public function fileService(): FileService
    {
        if (!isset($this->fileService)) {
            $this->fileService = app(FileService::class);
        }
        return $this->fileService;
    }

    public function storeFile(
        ?UploadedFile $file,
        string $field,
        string $directory,
        bool $public = true
    ): bool {
        if (!$file) {
            return false;
        }

        $oldPath = $this->{$field};
        $newPath = $this->fileService()->storeFile($file, $directory, $oldPath, $public);

        if ($newPath) {
            $this->{$field} = $newPath;
            return true;
        }

        return false;
    }

    public function getFileUrl(string $field): ?string
    {
        $path = $this->{$field};
        
        if (!$path) {
            return null;
        }

        return $this->fileService()->getFileUrl($path);
    }
}
```

## 7. Enum Pattern

### Опис
Використання PHP 8.1 Enums для типобезпечних констант з додатковими методами.

### Приклад
```php
// app/Enums/Role.php
enum Role: string
{
    case USER = 'user';
    case ADMIN = 'admin';
    case MODERATOR = 'moderator';

    public function name(): string
    {
        return match ($this) {
            self::USER => 'Користувач',
            self::ADMIN => 'Адміністратор',
            self::MODERATOR => 'Модератор',
        };
    }

    public function getIcon(): string
    {
        return match ($this) {
            self::ADMIN => 'heroicon-s-bug-ant',
            self::MODERATOR => 'heroicon-s-star',
            self::USER => 'heroicon-s-user',
        };
    }

    public function getBadgeColor(): string
    {
        return match ($this) {
            self::ADMIN => 'danger',
            self::MODERATOR => 'warning',
            self::USER => 'success',
        };
    }
}
```

## 8. Factory Pattern

### Опис
Використовується для створення тестових даних та об'єктів моделей.

### Приклад
```php
// database/factories/AnimeFactory.php
class AnimeFactory extends Factory
{
    protected $model = Anime::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(3),
            'original_title' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph(),
            'kind' => $this->faker->randomElement(Kind::cases()),
            'status' => $this->faker->randomElement(Status::cases()),
            'episodes_count' => $this->faker->numberBetween(1, 50),
            'imdb_score' => $this->faker->randomFloat(1, 1, 10),
            'first_air_date' => $this->faker->date(),
        ];
    }
}
```

## 9. Repository Pattern (через Eloquent)

### Опис
Реалізований через Eloquent ORM з використанням кастомних Query Builders та Scopes.

### Приклад Global Scope
```php
// app/Models/Scopes/PublishedScope.php
class PublishedScope implements Scope
{
    public function apply(Builder $builder, Model $model): void
    {
        $builder->where('is_published', true);
    }
}

// Використання в моделі
#[ScopedBy([PublishedScope::class])]
class Anime extends Model
{
    // Автоматично застосовується PublishedScope
}
```

## 10. Observer Pattern

### Опис
Реалізований через Laravel Events та Model Events для реагування на зміни в моделях.

### Приклад
```php
// app/Models/User.php
class User extends Model
{
    protected static function booted(): void
    {
        static::created(function (User $user) {
            // Створення досягнення для нового користувача
            CreateAchievementUser::dispatch($user, Achievement::WELCOME);
        });

        static::updated(function (User $user) {
            // Логування змін профілю
            if ($user->wasChanged(['name', 'email'])) {
                Log::info('User profile updated', ['user_id' => $user->id]);
            }
        });
    }
}
```

## Висновки

Використання цих патернів проектування в проекті Anime Site забезпечує:

1. **Модульність** - код розділений на логічні компоненти
2. **Повторне використання** - спільна функціональність винесена в traits та services
3. **Тестованість** - кожен компонент можна тестувати ізольовано
4. **Масштабованість** - легко додавати нову функціональність
5. **Підтримуваність** - зрозуміла структура та розділення відповідальностей
6. **Типобезпека** - використання DTO, Enums та Value Objects
7. **Продуктивність** - оптимізовані запити через Query Builders
