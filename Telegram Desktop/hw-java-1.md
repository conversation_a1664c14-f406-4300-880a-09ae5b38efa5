### Домашнє завдання з Java: Основи (Ти<PERSON><PERSON> даних, зм<PERSON><PERSON><PERSON><PERSON>, оператори, ввід/вивід)

#### Мета:
Закріпити знання про типи даних (`int`, `double`, `boolean`, `String`, `char`), створення та використання змінних, базові оператори (арифметичні, порівняння, логічні) та роботу з консоллю (`System.out.println`, `Scanner`).

#### Загальні вказівки:
1. Пишіть код у середовищі розробки (наприклад, IntelliJ IDEA або онлайн-компіляторі).
2. <PERSON><PERSON><PERSON><PERSON> програма повинна мати коментарі, що пояснюють, що робить код (ви самі їх пишіть, так запомните краще :))
3. Перевірте правильність вводу даних (наприклад, чи введено число, а не текст, де це важливо).
4. Використовуйте змістовні імена змінних (наприклад, `age` замість `a`).

#### Підзавдання:

##### 1. Калькулятор простих операцій
Напишіть програму, яка:
- Запитує у користувача два цілих числа (`int`).
- Виконує над ними операції: додавання, віднімання, множення, ділення та остачу від ділення.
- Виводить результати у форматі:
  ```
  5 + 3 = 8
  5 - 3 = 2
  5 * 3 = 15
  5 / 3 = 1
  5 % 3 = 2
  ```
- Додайте перевірку, щоб уникнути ділення на нуль.

##### 2. Конвертер температури
Створіть програму, яка:
- Запитує у користувача температуру в градусах Цельсія (`double`).
- Переводить її в градуси Фаренгейта за формулою: `F = C * 9/5 + 32`.
- Виводить результат із двома знаками після коми, наприклад:
  ```
  25.5°C = 77.90°F
  ```

##### 3. Перевірка парності числа
Напишіть програму, яка:
- Запитує у користувача ціле число (`int`).
- Визначає, чи є число парним, використовуючи оператор `%`.
- Виводить повідомлення: `Число 10 парне` або `Число 7 непарне`.

##### 4. Обчислення площі прямокутника
Створіть програму, яка:
- Запитує у користувача довжину та ширину прямокутника (`double`).
- Обчислює площу (`area = length * width`) та периметр (`perimeter = 2 * (length + width)`).
- Виводить результат у форматі:
  ```
  Площа: 20.5
  Периметр: 18.0
  ```
- Додайте перевірку, щоб довжина та ширина були додатними числами.

##### 5. Привітання користувача
Напишіть програму, яка:
- Запитує у користувача його ім’я (`String`) та вік (`int`).
- Виводить привітання у форматі:
  ```
  Привіт, Анна! Тобі 20 років.
  ```
- Якщо вік менший за 0 або більший за 120, виведіть повідомлення про помилку.

##### 6. Порівняння двох чисел
Створіть програму, яка:
- Запитує у користувача два числа (`double`).
- Порівнює їх за допомогою операторів `>`, `<`, `==`.
- Виводить результат у форматі:
  ```
  5.5 більше за 3.2
  ```
  або
  ```
  4.0 дорівнює 4.0
  ```

##### 7. Логічна перевірка доступу
Напишіть програму, яка:
- Запитує у користувача його вік (`int`) та чи має він студентський квиток (`boolean`, наприклад, введіть `true` або `false`).
- Виводить, чи може користувач отримати знижку в музеї, якщо знижка доступна для осіб віком до 18 або студентів.
- Приклад виводу:
  ```
  Ви можете отримати знижку!
  ```
  або
  ```
  Знижка недоступна.
  ```

##### 8. Обчислення середнього арифметичного
Створіть програму, яка:
- Запитує у користувача три оцінки (`int`) з діапазону від 1 до 12.
- Обчислює середнє арифметичне (`double`).
- Виводить результат із двома знаками після коми:
  ```
  Середнє арифметичне: 8.67
  ```
- Якщо хоча б одна оцінка поза діапазоном, виведіть повідомлення про помилку.

##### 9. Конвертер часу
Напишіть програму, яка:
- Запитує у користувача кількість секунд (`int`).
- Переводить секунди в години, хвилини та секунди.
- Виводить результат у форматі:
  ```
  3665 секунд = 1 годин, 1 хвилин, 5 секунд
  ```
- Додайте перевірку, щоб кількість секунд була невід’ємною.

##### 10. Творче завдання: Генератор імені супергероя
Створіть програму, яка:
- Запитує у користувача першу букву імені (`char`) та улюблене число (`int`).
- Генерує ім’я супергероя, комбінуючи введену букву з випадковим словом (наприклад, `S` + `hadow` = `Shadow`, `F` + `lame` = `Flame`) та додаючи число.
- Для простоти використовуйте кілька слів на вибір (наприклад, `"Storm"`, `"Blaze"`, `"Star"`).
- Приклад виводу:
  ```
  Ваше ім'я супергероя: SBlaze42
  ```

##### 11. Аналіз символу
Напишіть програму, яка:
- Запитує у користувача один символ (`char`).
- Визначає, чи є символ літерою, цифрою чи іншим символом (використовуйте методи `Character.isLetter()`, `Character.isDigit()`).
- Виводить результат:
  ```
  Символ 'A' є літерою.
  ```
  або
  ```
  Символ '5' є цифрою.
  ```

##### 12. Гра "Вгадай число"
Створіть програму, яка:
- Запитує у користувача число від 1 до 10 (`int`).
- Порівнює його з "загадкою" (наприклад, числом 7, заданим у коді).
- Виводить повідомлення:
  ```
  Вітаю, ви вгадали число!
  ```
  або
  ```
  На жаль, ви не вгадали. Спробуйте ще раз!
  ```

##### 13. Обчислення вартості покупки
Напишіть програму, яка:
- Запитує у користувача ціну товару (`double`) та кількість одиниць (`int`).
- Обчислює загальну вартість (`price * quantity`).
- Застосовує знижку 10%, якщо загальна сума перевищує 1000.
- Виводить результат:
  ```
  Загальна вартість: 900.0
  ```
  або
  ```
  Загальна вартість зі знижкою: 1080.0
  ```

##### 14. Творче завдання: Створення історії
Створіть програму, яка:
- Запитує у користувача його ім’я (`String`), улюблений колір (`String`) та число (`int`).
- Створює коротку історію, використовуючи ці дані, наприклад:
  ```
  Одного разу Анна вирушила до синього лісу з 42 зірками.
  ```
- Дозвольте користувачу ввести будь-який колір, але перевірте, що число додатне.

##### 15. Перевірка високосного року
Напишіть програму, яка:
- Запитує у користувача рік (`int`).
- Визначає, чи є рік високосним (рік високосний, якщо він ділиться на 4, але не ділиться на 100, або ділиться на 400).
- Виводить результат:
  ```
  2024 є високосним роком.
  ```
  або
  ```
  2023 не є високосним роком.
  ```

---

#### Додаткові вказівки:
- Виконуйте завдання поступово, починаючи з простих (1–5), якщо ви новачок.
- Для більшого виклику спробуйте завдання 10, 14 або 15.
- Якщо виникають помилки, перевірте правильність введення даних (наприклад, використання `nextInt()` чи `nextDouble()` у `Scanner`).
- Спробуйте оптимізувати код, уникаючи повторень.

#### Приклад структури програми (для завдання 1):
```java
import java.util.Scanner;

public class Main {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("Введіть перше число:");
        int a = scanner.nextInt();
        System.out.println("Введіть друге число:");
        int b = scanner.nextInt();
        
        if (b != 0) {
            System.out.println(a + " + " + b + " = " + (a + b));
            System.out.println(a + " - " + b + " = " + (a - b));
            System.out.println(a + " * " + b + " = " + (a * b));
            System.out.println(a + " / " + b + " = " + (a / b));
            System.out.println(a + " % " + b + " = " + (a % b));
        } else {
            System.out.println("Помилка: ділення на нуль!");
        }
        
        scanner.close();
    }
}
```