```sql
-- Таблиця користувачів
CREATE TABLE users (
    id UUID PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    profile_picture VARCHAR(255),
    bio TEXT,
    is_public BOOLEAN DEFAULT TRUE,
    birthday DATE,
    gender VARCHAR(20),
    location VARCHAR(100),
    last_login TIMESTAMP,
    role VARCHAR(20) NOT NULL DEFAULT 'user' CHECK (role IN ('user', 'author', 'librarian', 'admin')),
    social_media_links JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблиця видавництв
CREATE TABLE publishers (
    id UUID PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    description TEXT,
    website VARCHAR(255),
    country VARCHAR(50),
    founded_year INTEGER,
    logo VARCHAR(255),
    contact_email VARCHAR(255),
    phone VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Створення ENUM для type_of_works
CREATE TYPE author_work_type AS ENUM (
    'novelist',
    'short_story_writer',
    'poet',
    'playwright',
    'screenwriter',
    'essayist',
    'biographer',
    'memoirist',
    'historian',
    'journalist',
    'science_writer',
    'self_help_writer',
    'children_writer',
    'young_adult_writer',
    'graphic_novelist',
    'fantasy_writer',
    'sci_fi_writer',
    'mystery_writer',
    'romance_writer',
    'horror_writer',
    'other'
);

-- Таблиця авторів
CREATE TABLE authors (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    bio TEXT,
    birth_date DATE,
    birth_place VARCHAR(100),
    nationality VARCHAR(50),
    type_of_work author_work_type,
    website VARCHAR(255),
    profile_picture VARCHAR(255),
    death_date DATE,
    social_media_links JSONB, -- Масив рядків (посилання на соц мережі)
    media_images JSONB, -- Масив рядків (посилання або шляхи до картинок)
    media_videos JSONB, -- Масив рядків (посилання на відео)
    fun_facts JSONB,    -- Масив рядків (цікаві факти)
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблиця "Користувач буде управляти поточним автором"
CREATE TABLE user_authors (
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES authors(id) ON DELETE CASCADE,
    PRIMARY KEY (user_id, author_id)
);

-- Створення ENUM для статусу номінації
CREATE TYPE nomination_status AS ENUM ('winner', 'finalist');

-- Таблиця нагород
CREATE TABLE awards (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    year INTEGER NOT NULL,
    description TEXT,
    organizer VARCHAR(255), -- Організатор нагороди (наприклад, "The Booker Prize Foundation")
    country VARCHAR(50),    -- Країна нагороди
    ceremony_date DATE,     -- Дата церемонії
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблиця номінацій
CREATE TABLE nominations (
    id UUID PRIMARY KEY,
    award_id UUID NOT NULL REFERENCES awards(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL, -- Назва номінації (наприклад, "Найкращий роман")
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблиця записів номінацій (переможці, фіналісти)
CREATE TABLE nomination_entries (
    id UUID PRIMARY KEY,
    nomination_id UUID NOT NULL REFERENCES nominations(id) ON DELETE CASCADE,
    book_id UUID REFERENCES books(id) ON DELETE SET NULL,   -- Книга-номінант (опціонально)
    author_id UUID REFERENCES authors(id) ON DELETE SET NULL, -- Автор-номінант (опціонально)
    status nomination_status NOT NULL, -- Статус: переможець чи фіналіст
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_book_or_author CHECK (book_id IS NOT NULL OR author_id IS NOT NULL) -- Один із них має бути заповнений
);

-- Таблиця серій книг
CREATE TABLE book_series (
    id UUID PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    total_books INTEGER DEFAULT 0,
    is_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Створення ENUM для мов
CREATE TYPE book_language AS ENUM (
    'aa', 'ab', 'af', 'ak', 'am', 'an', 'ar', 'as', 'av', 'ay', 
    'az', 'ba', 'be', 'bg', 'bh', 'bi', 'bm', 'bn', 'bo', 'br', 
    'bs', 'ca', 'ce', 'ch', 'co', 'cr', 'cs', 'cu', 'cv', 'cy', 
    'da', 'de', 'dv', 'dz', 'ee', 'el', 'en', 'eo', 'es', 'et', 
    'eu', 'fa', 'ff', 'fi', 'fj', 'fo', 'fr', 'fy', 'ga', 'gd', 
    'gl', 'gn', 'gu', 'gv', 'ha', 'he', 'hi', 'ho', 'hr', 'ht', 
    'hu', 'hy', 'hz', 'ia', 'id', 'ie', 'ig', 'ii', 'ik', 'io', 
    'is', 'it', 'iu', 'ja', 'jv', 'ka', 'kg', 'ki', 'kj', 'kk', 
    'kl', 'km', 'kn', 'ko', 'kr', 'ks', 'ku', 'kv', 'kw', 'ky', 
    'la', 'lb', 'lg', 'li', 'ln', 'lo', 'lt', 'lu', 'lv', 'mg', 
    'mh', 'mi', 'mk', 'ml', 'mn', 'mr', 'ms', 'mt', 'my', 'na', 
    'nb', 'nd', 'ne', 'ng', 'nl', 'nn', 'no', 'nr', 'nv', 'ny', 
    'oc', 'oj', 'om', 'or', 'os', 'pa', 'pi', 'pl', 'ps', 'pt', 
    'qu', 'rm', 'rn', 'ro', 'ru', 'rw', 'sa', 'sc', 'sd', 'se', 
    'sg', 'si', 'sk', 'sl', 'sm', 'sn', 'so', 'sq', 'sr', 'ss', 
    'st', 'su', 'sv', 'sw', 'ta', 'te', 'tg', 'th', 'ti', 'tk', 
    'tl', 'tn', 'to', 'tr', 'ts', 'tt', 'tw', 'ty', 'ug', 'uk', 
    'ur', 'uz', 've', 'vi', 'vo', 'wa', 'wo', 'xh', 'yi', 'yo', 
    'za', 'zh', 'zu'
);

-- Створення ENUM для вікових обмежень
CREATE TYPE age_rating AS ENUM ('0+', '6+', '12+', '16+', '18+');

-- Оновлення таблиці books
CREATE TABLE books (
    id UUID PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    plot TEXT, -- Опис сюжету
    history TEXT, -- Історія створення книги
    series_id UUID REFERENCES book_series(id) ON DELETE SET NULL,
    number_in_series INTEGER,
    page_count INTEGER,
    languages JSONB, -- Масив мов (перша — оригінальна)
    age_restriction age_rating, -- Вікові обмеження
    cover_image VARCHAR(255),
    fun_facts JSONB, -- Масив цікавих фактів
    adaptations JSONB, -- Масив об’єктів {year, title, link, image?}
    is_bestseller BIT,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Створення ENUM для типу обкладинки
CREATE TYPE cover_type AS ENUM ('hardcover', 'paperback', 'other');

-- Таблиця зв’язку книг і видавництв
CREATE TABLE book_publishers (
    book_id UUID NOT NULL REFERENCES books(id) ON DELETE CASCADE,
    publisher_id UUID NOT NULL REFERENCES publishers(id) ON DELETE CASCADE,
    published_date DATE, -- Дата видання для конкретного видавця
    isbn VARCHAR(13) UNIQUE, -- ISBN для цього видання
    circulation INTEGER, -- Тираж
    format VARCHAR(50), -- Формат (наприклад, "84x108/32 (130x200 мм)")
    cover_type cover_type, -- Тип обкладинки
    translator VARCHAR(100), -- Перекладач
    edition INTEGER, -- Номер видання
    price BIGINT, -- Ціна (копійски)
    binding VARCHAR(50), -- Тип палітурки
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (book_id, publisher_id)
);

-- Таблиця жанрів
CREATE TABLE genres (
    id UUID PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    parent_id UUID REFERENCES genres(id) ON DELETE CASCADE,
    description TEXT,
    book_count INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблиця зв’язку книг і авторів
CREATE TABLE book_authors (
    book_id UUID NOT NULL REFERENCES books(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES authors(id) ON DELETE CASCADE,
    PRIMARY KEY (book_id, author_id)
);

-- Таблиця зв’язку книг і жанрів
CREATE TABLE book_genres (
    book_id UUID NOT NULL REFERENCES books(id) ON DELETE CASCADE,
    genre_id UUID NOT NULL REFERENCES genres(id) ON DELETE CASCADE,
    PRIMARY KEY (book_id, genre_id)
);

-- Створення ENUM для формату читання
CREATE TYPE reading_format AS ENUM ('physical', 'ebook', 'audiobook', 'other');

-- Таблиця полиць
CREATE TABLE shelves (
    id UUID PRIMARY KEY,                   -- Унікальний ідентифікатор полиці
    user_id UUID REFERENCES users(id) ON DELETE CASCADE, -- Користувач, що створив полицю (NULL для типових полиць)
    name VARCHAR(50) NOT NULL,             -- Назва полиці (наприклад, "read", "favorites")
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата оновлення
    UNIQUE (user_id, name)                 -- Унікальність назви полиці для користувача
);

-- Оновлення таблиці user_books
CREATE TABLE user_books (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор запису
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID користувача
    book_id UUID NOT NULL REFERENCES books(id) ON DELETE CASCADE, -- ID книги
    shelf_id UUID NOT NULL REFERENCES shelves(id) ON DELETE CASCADE, -- ID полиці
    start_date DATE,                       -- Дата початку читання книги
    read_date DATE,                        -- Дата завершення читання книги
    progress_pages INTEGER,                 -- Кількість прочитаних сторінок
    is_private BOOLEAN DEFAULT FALSE,       -- Чи є запис приватним
    rating INTEGER CHECK (rating >= 1 AND rating <= 5), -- Оцінка книги користувачем (1-5)
    notes TEXT,                            -- Короткі нотатки користувача про книгу
    reading_format reading_format,          -- Формат читання (фізична, електронна, аудіокнига)
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення запису
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата оновлення запису
    UNIQUE (user_id, book_id)               -- Унікальність комбінації користувач-книга
);

-- Таблиця оцінок (з рецензіями)
CREATE TABLE ratings (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор оцінки
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID користувача, який залишив оцінку
    book_id UUID NOT NULL REFERENCES books(id) ON DELETE CASCADE, -- ID книги, яку оцінено
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5), -- Оцінка (1-5)
    review TEXT,                           -- Текст рецензії (опціонально)
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення оцінки
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата оновлення оцінки
    UNIQUE (user_id, book_id)               -- Унікальність комбінації користувач-книга
);

-- Таблиця лайків (поліморфна)
CREATE TABLE likes (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор лайка
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID користувача, який поставив лайк
    likeable_id UUID NOT NULL,              -- ID сутності, яку лайкнули
    likeable_type VARCHAR(50) NOT NULL,     -- Тип сутності (наприклад, 'ratings', 'comments', 'quotes')
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення лайка
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата оновлення лайка
    UNIQUE (user_id, likeable_id, likeable_type) -- Унікальність комбінації користувач-сутність
);

-- Таблиця коментарів (поліморфна)
CREATE TABLE comments (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор коментаря
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID користувача, який залишив коментар
    commentable_id UUID NOT NULL,           -- ID сутності, яку коментують
    commentable_type VARCHAR(50) NOT NULL,  -- Тип сутності (наприклад, 'ratings', 'group_posts', 'quotes')
    content TEXT NOT NULL,                 -- Текст коментаря
    parent_id UUID REFERENCES comments(id) ON DELETE CASCADE, -- ID батьківського коментаря (для вкладених коментарів)
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення коментаря
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Дата оновлення коментаря
);

-- Таблиця обраного (поліморфна)
CREATE TABLE favorites (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор запису в обраному
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID користувача, який додав до обраного
    favoriteable_id UUID NOT NULL,          -- ID сутності, доданої до обраного
    favoriteable_type VARCHAR(50) NOT NULL, -- Тип сутності (наприклад, 'ratings', 'group_posts', 'quotes')
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата додавання до обраного
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата оновлення запису
    UNIQUE (user_id, favoriteable_id, favoriteable_type) -- Унікальність комбінації користувач-сутність
);

-- Таблиця цитат
CREATE TABLE quotes (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    book_id UUID NOT NULL REFERENCES books(id) ON DELETE CASCADE,
    text TEXT NOT NULL,
    page_number INTEGER,
    contains_spoilers BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Створення ENUM для типу скарги
CREATE TYPE report_type AS ENUM (
    'spam',          -- Спам або реклама
    'offensive',     -- Образливий вміст
    'inappropriate', -- Невідповідний вміст
    'spoilers',      -- Ненавмисні спойлери
    'copyright',     -- Порушення авторських прав
    'other'          -- Інше
);

-- Створення ENUM для статусу скарги
CREATE TYPE report_status AS ENUM (
    'pending',       -- Очікує розгляду
    'reviewed',      -- Переглянуто
    'resolved',      -- Вирішено
    'dismissed'      -- Відхилено
);

-- Таблиця скарг (поліморфна)
CREATE TABLE reports (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор скарги
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID користувача, який подав скаргу
    reportable_id UUID NOT NULL,            -- ID сутності, на яку подано скаргу
    reportable_type VARCHAR(50) NOT NULL,   -- Тип сутності (наприклад, 'ratings', 'group_posts', 'quotes')
    report_type report_type NOT NULL,       -- Тип скарги
    description TEXT,                       -- Опис скарги (опціонально)
    status report_status DEFAULT 'pending', -- Статус обробки скарги
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення скарги
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата оновлення скарги
    UNIQUE (user_id, reportable_id, reportable_type) -- Унікальність комбінації користувач-сутність
);

-- Таблиця тегів
CREATE TABLE tags (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор тегу
    name VARCHAR(50) NOT NULL UNIQUE,       -- Назва тегу (наприклад, "чарівник", "герой")
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення тегу
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Дата оновлення тегу
);

-- Таблиця поліморфних зв’язків для тегів
CREATE TABLE taggables (
    tag_id UUID NOT NULL REFERENCES tags(id) ON DELETE CASCADE, -- ID тегу
    taggable_id UUID NOT NULL,              -- ID сутності, до якої прив’язаний тег
    taggable_type VARCHAR(50) NOT NULL,     -- Тип сутності (наприклад, 'characters', 'books', 'quotes')
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення зв’язку
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата оновлення зв’язку
    PRIMARY KEY (tag_id, taggable_id, taggable_type) -- Унікальність комбінації тег-сутність
);

-- Таблиця персонажів
CREATE TABLE characters (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор персонажа
    book_id UUID NOT NULL REFERENCES books(id) ON DELETE CASCADE, -- ID книги, де з’являється персонаж
    name VARCHAR(255) NOT NULL,             -- Основне ім’я персонажа (наприклад, "Альбус Дамблдор")
    other_names JSONB,                      -- Інші імена (наприклад, ["Albus Percival Wulfric Brian Dumbledore"])
    race VARCHAR(50),                       -- Раса персонажа (наприклад, "human")
    nationality VARCHAR(50),                -- Національність (наприклад, "Англичанин")
    residence VARCHAR(100),                 -- Місце проживання (наприклад, "Хогвартс")
    biography TEXT,                         -- Біографія персонажа
    fun_facts JSONB,                        -- Цікаві факти (масив рядків)
    links JSONB,                            -- Зовнішні посилання (масив об’єктів {title, url})
    media_images JSONB, -- Масив рядків (посилання або шляхи до картинок)
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення персонажа
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Дата оновлення персонажа
);
-- Таблиця нотаток
CREATE TABLE notes (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    book_id UUID NOT NULL REFERENCES books(id) ON DELETE CASCADE,
    text TEXT NOT NULL,
    page_number INTEGER,
    contains_spoilers BOOLEAN DEFAULT FALSE,
    is_private BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Створення ENUM для політики приєднання до групи
CREATE TYPE join_policy AS ENUM (
    'open',         -- Відкрита (будь-хто може приєднатися)
    'request',      -- За запитом (потрібне схвалення)
    'invite_only'   -- Лише за запрошенням
);

-- Створення ENUM для політики постингу
CREATE TYPE post_policy AS ENUM (
    'all',          -- Усі учасники можуть постити
    'moderators',   -- Лише модератори та адміни
    'admins'        -- Лише адміни
);

-- Створення ENUM для статусу членства
CREATE TYPE member_status AS ENUM (
    'active',       -- Активний учасник
    'pending',      -- Очікує схвалення
    'banned'        -- Забанений
);

-- Створення ENUM для категорії посту
CREATE TYPE post_category AS ENUM (
    'general',      -- Загальні дискусії
    'spoilers',     -- Дискусії зі спойлерами
    'recommendations', -- Рекомендації книг
    'off_topic',    -- Поза темою
    'other'         -- Інше
);

-- Створення ENUM для статусу посту
CREATE TYPE post_status AS ENUM (
    'active',       -- Активний пост
    'pending',      -- Очікує модерації
    'deleted'       -- Видалений
);

-- Створення ENUM для статусу події
CREATE TYPE event_status AS ENUM (
    'upcoming',     -- Майбутня подія
    'ongoing',      -- Триває
    'past',         -- Завершена
    'canceled'      -- Скасована
);

-- Створення ENUM для статусу запрошення
CREATE TYPE invitation_status AS ENUM (
    'pending',      -- Очікує підтвердження
    'accepted',     -- Прийнято
    'declined',     -- Відхилено
);

-- Оновлення таблиці груп
CREATE TABLE groups (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор групи
    name VARCHAR(100) NOT NULL,             -- Назва групи
    description TEXT,                       -- Опис групи
    creator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID творця групи
    is_public BOOLEAN DEFAULT TRUE,         -- Чи є група публічною
    cover_image VARCHAR(255),               -- Посилання на обкладинку групи
    rules TEXT,                             -- Правила групи
    join_policy join_policy DEFAULT 'open', -- Політика приєднання
    post_policy post_policy DEFAULT 'all',  -- Політика постингу
    member_count INTEGER DEFAULT 0,         -- Кількість учасників
    is_active BOOLEAN DEFAULT TRUE,         -- Чи є група активною
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення групи
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Дата оновлення групи
);

-- Оновлення таблиці зв’язку груп і учасників
CREATE TABLE group_members (
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE, -- ID групи
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID користувача
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'moderator', 'member')) DEFAULT 'member', -- Роль учасника
    status member_status DEFAULT 'active',  -- Статус членства
    joined_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата приєднання
    PRIMARY KEY (group_id, user_id)         -- Унікальність комбінації група-користувач
);

-- Оновлення таблиці постів у групах
CREATE TABLE group_posts (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор посту
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE, -- ID групи
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID автора посту
    content TEXT NOT NULL,                 -- Текст посту
    category post_category DEFAULT 'general', -- Категорія посту
    is_pinned BOOLEAN DEFAULT FALSE,        -- Чи закріплений пост
    status post_status DEFAULT 'active',    -- Статус посту
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення посту
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Дата оновлення посту
);

-- Таблиця подій групи
CREATE TABLE group_events (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор події
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE, -- ID групи
    creator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID творця події
    title VARCHAR(255) NOT NULL,            -- Назва події
    description TEXT,                       -- Опис події
    event_date TIMESTAMP,                   -- Дата і час події
    location VARCHAR(255),                  -- Місце проведення (опціонально)
    status event_status DEFAULT 'upcoming', -- Статус події
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення події
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Дата оновлення події
);

-- Таблиця RSVP для подій
CREATE TABLE event_rsvps (
    event_id UUID NOT NULL REFERENCES group_events(id) ON DELETE CASCADE, -- ID події
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID користувача
    response VARCHAR(20) NOT NULL CHECK (response IN ('going', 'maybe', 'not_going')), -- Відповідь
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення RSVP
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата оновлення RSVP
    PRIMARY KEY (event_id, user_id)         -- Унікальність комбінації подія-користувач
);

-- Таблиця опитувань
CREATE TABLE group_polls (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор опитування
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE, -- ID групи
    creator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID творця опитування
    question TEXT NOT NULL,                 -- Питання опитування
    is_active BOOLEAN DEFAULT TRUE,         -- Чи є опитування активним
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення опитування
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Дата оновлення опитування
);

-- Таблиця варіантів відповідей опитування
CREATE TABLE poll_options (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор варіанту
    poll_id UUID NOT NULL REFERENCES group_polls(id) ON DELETE CASCADE, -- ID опитування
    text VARCHAR(255) NOT NULL,             -- Текст варіанту
    vote_count INTEGER DEFAULT 0,           -- Кількість голосів
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення варіанту
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Дата оновлення варіанту
);

-- Таблиця голосів за опитування
CREATE TABLE poll_votes (
    poll_id UUID NOT NULL REFERENCES group_polls(id) ON DELETE CASCADE, -- ID опитування
    option_id UUID NOT NULL REFERENCES poll_options(id) ON DELETE CASCADE, -- ID варіанту
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID користувача
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата голосування
    PRIMARY KEY (poll_id, user_id)          -- Унікальність комбінації опитування-користувач
);

-- Таблиця запрошень до груп
CREATE TABLE group_invitations (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор запрошення
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE, -- ID групи
    inviter_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID користувача, який запросив
    invitee_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID запрошеного користувача
    status invitation_status DEFAULT 'pending', -- Статус запрошення
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення запрошення
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата оновлення запрошення
    UNIQUE (group_id, invitee_id)           -- Унікальність комбінації група-запрошений
);

-- Таблиця логів модерації
CREATE TABLE group_moderation_logs (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор логу
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE, -- ID групи
    moderator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID модератора
    action VARCHAR(50) NOT NULL,            -- Дія (наприклад, "delete_post", "ban_member")
    target_id UUID,                         -- ID цілі (наприклад, посту, учасника)
    target_type VARCHAR(50),                -- Тип цілі (наприклад, 'group_posts', 'group_members')
    description TEXT,                       -- Опис дії
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення логу
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Дата оновлення логу
);

-- Таблиця підписників
CREATE TABLE follows (
    follower_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    followed_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    PRIMARY KEY (follower_id, followed_id),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблиця історії переглядів (поліморфна)
CREATE TABLE view_histories (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор перегляду
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID користувача, який здійснив перегляд
    viewable_id UUID NOT NULL,              -- ID сутності, яку переглянули
    viewable_type VARCHAR(50) NOT NULL,     -- Тип сутності (наприклад, 'books', 'characters', 'group_posts')
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення запису
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата оновлення запису
    UNIQUE (user_id, viewable_id, viewable_type) -- Унікальність комбінації користувач-сутність
);

-- Створення ENUM для типу посту
CREATE TYPE post_type AS ENUM (
    'article',      -- Стаття
    'story',        -- Історія
    'lifehack'      -- Лайфхак
);

-- Створення ENUM для статусу посту
CREATE TYPE post_status AS ENUM (
    'draft',        -- Чернетка
    'pending',      -- Очікує модерації
    'published',    -- Опубліковано
    'archived'      -- Архівовано
);

-- Таблиця постів (об’єднує статті, історії, лайфхаки)
CREATE TABLE posts (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор посту
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID автора посту
    book_id UUID REFERENCES books(id) ON DELETE SET NULL, -- ID книги (опціонально, для історій/лайфхаків)
    author_id UUID REFERENCES authors(id) ON DELETE SET NULL, -- ID автора (опціонально, для історій/лайфхаків)
    type post_type NOT NULL,                -- Тип посту (article, story, lifehack)
    title VARCHAR(255) NOT NULL,            -- Заголовок посту
    slug VARCHAR(255),                      -- URL-friendly slug (для статей, опціонально)
    content TEXT NOT NULL,                 -- Текст посту
    cover_image VARCHAR(255),               -- Посилання на обкладинку посту
    status post_status DEFAULT 'draft',     -- Статус посту
    published_at TIMESTAMP,                 -- Дата публікації
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення посту
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата оновлення посту
    CONSTRAINT unique_slug CHECK (type != 'article' OR slug IS NOT NULL), -- slug обов’язковий для статей
    UNIQUE (slug)                           -- Унікальність slug
);

-- Таблиця підбірок
CREATE TABLE collections (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор підбірки
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID творця підбірки
    title VARCHAR(255) NOT NULL,            -- Назва підбірки
    description TEXT,                       -- Опис підбірки
    cover_image VARCHAR(255),               -- Посилання на обкладинку підбірки
    is_public BOOLEAN DEFAULT TRUE,         -- Чи є підбірка публічною
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення підбірки
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Дата оновлення підбірки
);

-- Таблиця зв’язку підбірок і книг
CREATE TABLE collection_books (
    collection_id UUID NOT NULL REFERENCES collections(id) ON DELETE CASCADE, -- ID підбірки
    book_id UUID NOT NULL REFERENCES books(id) ON DELETE CASCADE, -- ID книги
    order_index INTEGER DEFAULT 0,          -- Порядок книги у підбірці
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата додавання книги
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата оновлення запису
    PRIMARY KEY (collection_id, book_id)    -- Унікальність комбінації підбірка-книга
);

-- Створення ENUM для статусу запитання
CREATE TYPE question_status AS ENUM (
    'pending',      -- Очікує модерації
    'approved',     -- Схвалено
    'rejected',     -- Відхилено
    'answered'      -- Відповідь надана
);

-- Створення ENUM для статусу відповіді
CREATE TYPE answer_status AS ENUM (
    'draft',        -- Чернетка
    'pending',      -- Очікує модерації
    'published',    -- Опубліковано
    'rejected'      -- Відхилено
);

-- Таблиця запитань до авторів
CREATE TABLE author_questions (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор запитання
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID користувача, який задав запитання
    author_id UUID NOT NULL REFERENCES authors(id) ON DELETE CASCADE, -- ID автора, якому адресоване запитання
    book_id UUID REFERENCES books(id) ON DELETE SET NULL, -- ID книги (опціонально)
    content TEXT NOT NULL,                 -- Текст запитання
    status question_status DEFAULT 'pending', -- Статус запитання
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення запитання
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Дата оновлення запитання
);

-- Таблиця відповідей авторів
CREATE TABLE author_answers (
    id UUID PRIMARY KEY,                    -- Унікальний ідентифікатор відповіді
    question_id UUID NOT NULL REFERENCES author_questions(id) ON DELETE CASCADE, -- ID запитання
    author_id UUID NOT NULL REFERENCES authors(id) ON DELETE CASCADE, -- ID автора, який відповів
    content TEXT NOT NULL,                 -- Текст відповіді
    status answer_status DEFAULT 'draft',   -- Статус відповіді
    published_at TIMESTAMP,                 -- Дата публікації відповіді
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Дата створення відповіді
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP  -- Дата оновлення відповіді
);

-- Таблиця сповіщень (стандартний від ларавеля)
CREATE TABLE notifications (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL CHECK (type IN ('friend_activity', 'group_post', 'new_book', 'recommendation')),
    content TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Таблиця статистики читання
CREATE TABLE reading_stats (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    year INTEGER NOT NULL,
    books_read INTEGER DEFAULT 0,
    pages_read INTEGER DEFAULT 0,
    genres_read JSONB,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_id, year)
);

-- Тригер для оновлення average_rating у books
CREATE OR REPLACE FUNCTION update_average_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE books
    SET average_rating = COALESCE((
        SELECT AVG(rating)
        FROM ratings
        WHERE book_id = NEW.book_id
    ), 0)
    WHERE id = NEW.book_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_average_rating
AFTER INSERT OR UPDATE OR DELETE ON ratings
FOR EACH ROW
EXECUTE FUNCTION update_average_rating();

-- Тригер для оновлення book_count у genres
CREATE OR REPLACE FUNCTION update_genre_book_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE genres
    SET book_count = (
        SELECT COUNT(*)
        FROM book_genres
        WHERE genre_id = genres.id
    )
    WHERE id IN (
        SELECT genre_id
        FROM book_genres
        WHERE genre_id = genres.id
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_genre_book_count
AFTER INSERT OR DELETE ON book_genres
FOR EACH ROW
EXECUTE FUNCTION update_genre_book_count();

-- Тригер для оновлення total_books у book_series
CREATE OR REPLACE FUNCTION update_series_total_books()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE book_series
    SET total_books = (
        SELECT COUNT(*)
        FROM books
        WHERE series_id = book_series.id
    )
    WHERE id IN (
        SELECT series_id
        FROM books
        WHERE series_id = book_series.id
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_series_total_books
AFTER INSERT OR DELETE ON books
FOR EACH ROW
EXECUTE FUNCTION update_series_total_books();

-- Тригер для оновлення book_count у book_lists
CREATE OR REPLACE FUNCTION update_book_list_book_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE book_lists
    SET book_count = (
        SELECT COUNT(*)
        FROM book_list_book
        WHERE book_list_id = book_lists.id
    )
    WHERE id IN (
        SELECT book_list_id
        FROM book_list_book
        WHERE book_list_id = book_lists.id
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_book_list_book_count
AFTER INSERT OR DELETE ON book_list_book
FOR EACH ROW
EXECUTE FUNCTION update_book_list_book_count();

-- Тригер для оновлення member_count у groups
CREATE OR REPLACE FUNCTION update_group_member_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE groups
    SET member_count = (
        SELECT COUNT(*)
        FROM group_members
        WHERE group_id = groups.id
    )
    WHERE id IN (
        SELECT group_id
        FROM group_members
        WHERE group_id = groups.id
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_group_member_count
AFTER INSERT OR DELETE ON group_members
FOR EACH ROW
EXECUTE FUNCTION update_group_member_count();
```