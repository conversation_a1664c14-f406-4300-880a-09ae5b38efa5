# ITSTEP VPN Setup

## Налаштування завершено! 🎉

Ваш VPN для ITSTEP тепер налаштований і готовий до використання.

## Використання

Просто введіть `vpn` в терміналі для керування VPN:

### Основні команди:

```bash
vpn                 # Перемкнути стан VPN (увімкнути/вимкнути)
vpn toggle          # Те саме, що і просто vpn
vpn start           # Запустити VPN
vpn on              # Запустити VPN
vpn up              # Запустити VPN
vpn stop            # Зупинити VPN
vpn off             # Зупинити VPN
vpn down            # Зупинити VPN
vpn status          # Показати поточний статус VPN
vpn stat            # Показати поточний статус VPN
```

## Файли

- `ITSTEP.ovpn` - конфігурація OpenVPN
- `vpn-credentials.txt` - логін та пароль (захищений)
- `vpn-toggle.sh` - скрипт керування VPN
- `/usr/local/bin/vpn` - символічне посилання для глобального доступу

## Логін та пароль

- **Логін**: <EMAIL>
- **Пароль**: 6ZdSctJ3347F

## Безпека

Файл з паролем (`vpn-credentials.txt`) має обмежені права доступу. 
Рекомендується встановити права доступу тільки для власника:

```bash
chmod 600 /home/<USER>/Downloads/vpn-credentials.txt
```

## Статуси

- ✅ **Підключений** - VPN активний
- ❌ **Відключений** - VPN неактивний

## Примітки

- Скрипт автоматично перевіряє наявність OpenVPN
- Використовує sudo для запуску/зупинки VPN
- PID файл зберігається в `/tmp/openvpn-itstep.pid`
- Працює як в bash, так і в fish shell
